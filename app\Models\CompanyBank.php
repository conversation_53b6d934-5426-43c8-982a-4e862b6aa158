<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyBank extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'bank_acc_name',
		'bank_code',
        'bank_acc_no',
        'bank_acc_address',
        'bank_name',
        'bank_address',
		'bank_correspondent',
        'swift',
        'swift_correspondent',
		'routing_no',
		'transit',
		'tt_charge',
		'institution',
		'iban',
		'bsb',
		'branch_code',
		'sort_code',
		'branch_bank',
		'ABA',
		'IFSC',
		'custom_columns',
    ];

    protected $casts = [
        'custom_columns' => 'array',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /**
     * Get all delivered (existing) column names
     *
     * @return array
     */
    public static function getDeliveredColumns(): array
    {
        return [
            'company_id', 'bank_acc_name', 'bank_code', 'bank_acc_no', 'bank_acc_address',
            'bank_name', 'bank_address', 'bank_correspondent', 'swift', 'swift_correspondent',
            'routing_no', 'transit', 'tt_charge', 'institution', 'iban', 'bsb',
            'branch_code', 'sort_code', 'branch_bank', 'ABA', 'IFSC', 'is_default',
            'created_at', 'updated_at', 'deleted_at', 'custom_columns'
        ];
    }

    /**
     * Get existing custom column keys for this record
     *
     * @return array
     */
    public function getExistingCustomKeys(): array
    {
        return array_keys($this->custom_columns ?? []);
    }

    /**
     * Check if a key already exists (in delivered columns or custom columns)
     *
     * @param string $key
     * @return bool
     */
    public function keyExists(string $key): bool
    {
        return in_array($key, self::getDeliveredColumns()) ||
               in_array($key, $this->getExistingCustomKeys());
    }
}
