<div class="space-y-8 p-6 bg-white rounded shadow">

    @if(session()->has('message'))
        <div class="p-2 bg-green-200 text-green-900 rounded">
            {{ session('message') }}
        </div>
    @endif

    @foreach($sections as $index => $section)
        <div class="border p-4 rounded-lg shadow">
            <h3 class="font-semibold text-lg mb-4">Section: {{ ucfirst($section['type']) }}</h3>

            @php
                $grid = json_decode($section['layout'], true) ?? [];
            @endphp

            <table class="w-full border border-gray-300 mb-4">
                <tbody>
                    @foreach($grid as $r => $row)
                        <tr>
                            @foreach($row as $c => $cell)
                                <td class="border p-2 text-center" colspan="{{ $cell['colspan'] }}" rowspan="{{ $cell['rowspan'] }}">
                                    <div>
                                        <select wire:model="sections.{{ $index }}.layout.{{ $r }}.{{ $c }}.type" class="w-full mb-1 border rounded p-1">
                                            <option value="text">Text</option>
                                            <option value="image">Image</option>
                                        </select>

                                        <input type="text" wire:model="sections.{{ $index }}.layout.{{ $r }}.{{ $c }}.field"
                                               placeholder="Field (e.g. company.name)"
                                               class="w-full border rounded p-1" />
                                    </div>
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <button wire:click="saveSectionLayout({{ $index }})" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                💾 Simpan Section
            </button>
        </div>
    @endforeach

</div>
