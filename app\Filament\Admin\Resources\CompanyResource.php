<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CompanyResource\Pages;
use App\Filament\Admin\Resources\CompanyResource\RelationManagers;
use App\Filament\Admin\Resources\CompanyResource\RelationManagers\BanksRelationManager;
use App\Models\BusinessType;
use App\Models\Company;
use App\Models\Font;
use Filament\Forms\Components\{ColorPicker, Fieldset, FileUpload, Group, RichEditor, Select, TextInput, Toggle};
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\File;

class CompanyResource extends Resource
{
    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
	protected static ?string $activeNavigationIcon = 'heroicon-s-building-office-2';
	protected static ?string $navigationGroup = 'Masters Data';

    public static function form(Form $form): Form
    {
        return $form
			->columns(12)
            ->schema([
				Section::make('Basic Information')
					->columnSpanFull()
					->columns(12)
					->schema([
						Group::make()
							->columnSpan(8)
							->schema([
								TextInput::make('name')
									->required()
									->label('Company Name')
									->inlineLabel(),
								Select::make('business_type')
									->inlineLabel()
									->required()
									->searchable()
									->options(
										BusinessType::get()
											->mapWithKeys(fn ($business) => [
												$business->id => "{$business->name}"
											])
											->toArray()
									),
								RichEditor::make('address')
									->extraInputAttributes(['style' => 'min-height: 5.5rem; max-height: 5.5vh; overflow-y: auto;'])
									->inlineLabel()
									->helperText('Shift+Enter to add new row.')
									->toolbarButtons([]),
							]),
						Group::make()
							->columnSpan(4)
							->schema([
								TextInput::make('phone')
									->tel()
									->inlineLabel(),
								TextInput::make('email')
									->email()
									->inlineLabel(),
								Select::make('type')
									->required()
									->reactive()
									->inlineLabel()
									->options([
										1 => 'Client',
										2 => 'Internal'
									]),
							]),

					]),

				Section::make('Invoice Template')
					->columnSpanFull()
                    ->collapsible()
					->collapsed()
                    ->columns(2)
					->schema([
                        Fieldset::make('Layout')
                            ->columnSpan(1)
                            ->columns(2)
                            ->schema([
                                Select::make('template')
                                    ->searchable()
                                    ->inlineLabel()
                                    ->label('Main Layout')
									->columnSpan(2)
                                    ->options(
                                        collect(File::files(resource_path('views/invoice')))
                                            ->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
                                            ->mapWithKeys(fn ($file) => [
                                                str_replace('.blade.php', '', $file->getFilename()) =>
                                                ucfirst(str_replace('.blade.php', '', $file->getFilename())),
                                            ])
                                            ->toArray()
                                    ),
                                Select::make('templateheading')
                                    ->searchable()
                                    ->inlineLabel()
									->columnSpan(2)
                                    ->label('Heading Layout')
                                    ->options(
                                        collect(File::files(resource_path('views/template/kop')))
                                            ->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
                                            ->mapWithKeys(fn ($file) => [
                                                str_replace('.blade.php', '', $file->getFilename()) =>
                                                ucfirst(str_replace('.blade.php', '', $file->getFilename())),
                                            ])
                                            ->toArray()
                                    ),
                                Select::make('templatebillto')
                                    ->searchable()
                                    ->inlineLabel()
									->columnSpan(2)
                                    ->label('Billto Layout')
                                    ->options(
                                        collect(File::files(resource_path('views/template/billto')))
                                            ->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
                                            ->mapWithKeys(fn ($file) => [
                                                str_replace('.blade.php', '', $file->getFilename()) =>
                                                ucfirst(str_replace('.blade.php', '', $file->getFilename())),
                                            ])
                                            ->toArray()
                                    ),
                                Select::make('templatetable')
                                    ->searchable()
                                    ->inlineLabel()
									->columnSpan(2)
                                    ->label('Table Layout')
                                    ->options(
                                        collect(File::files(resource_path('views/template/table')))
                                            ->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
                                            ->mapWithKeys(fn ($file) => [
                                                str_replace('.blade.php', '', $file->getFilename()) =>
                                                ucfirst(str_replace('.blade.php', '', $file->getFilename())),
                                            ])
                                            ->toArray()
                                    ),
                                Select::make('templateinword')
                                    ->searchable()
                                    ->inlineLabel()
									->columnSpan(2)
                                    ->label('Words Layout')
                                    ->options(
                                        collect(File::files(resource_path('views/template/inwords')))
                                            ->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
                                            ->mapWithKeys(fn ($file) => [
                                                str_replace('.blade.php', '', $file->getFilename()) =>
                                                ucfirst(str_replace('.blade.php', '', $file->getFilename())),
                                            ])
                                            ->toArray()
                                    ),
                                Select::make('templatebankinfo')
                                    ->searchable()
                                    ->inlineLabel()
									->columnSpan(2)
                                    ->label('Bank Info Layout')
                                    ->options(
                                        collect(File::files(resource_path('views/template/bankinfo')))
                                            ->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
                                            ->mapWithKeys(fn ($file) => [
                                                str_replace('.blade.php', '', $file->getFilename()) =>
                                                ucfirst(str_replace('.blade.php', '', $file->getFilename())),
                                            ])
                                            ->toArray()
                                    ),

                				TextInput::make('signature_name')
                                	->helperText('Needed when Invoices has Signature name')
									->columnSpan(2)
                                    ->inlineLabel(),


                                FileUpload::make('logo')
                                    ->image()
                                    ->columnSpan(1)
                                    ->disk('public')
                                    ->label('Company Logo')
                                    ->panelAspectRatio('1:1')
                                    ->panelLayout('integrated')
                                    ->imagePreviewHeight('50')
                                    ->fetchFileInformation(false)
                                    ->directory('logo')
                                    ->loadingIndicatorPosition('left'),
                                FileUpload::make('signature')
                                    ->image()
                                    ->columnSpan(1)
                                    ->disk('public')
                                    ->label('Signature')
                                    ->panelAspectRatio('1:1')
                                    ->panelLayout('integrated')
                                    ->imagePreviewHeight('50')
                                    ->fetchFileInformation(false)
                                    ->directory('signature')
                                    ->loadingIndicatorPosition('left')
                                    ->helperText('Needed when Invoices has Signature.'),
                            ]),

                        Fieldset::make('Style')
                            ->columnSpan(1)
                            ->columns(1)
                            ->schema([
                                Select::make('font_id')
                                    ->searchable()
                                    ->label('Font')
                                    ->required()
                                    ->inlineLabel()
                                    ->options(
                                        Font::get()
                                            ->mapWithKeys(fn ($font) => [
                                                $font->id => "{$font->name}"
                                            ])
                                            ->toArray()
                                    ),

                                Select::make('heading_size')
                                    ->searchable()
                                    ->inlineLabel()
                                    ->label('Header Size')
                                    ->options([
                                        'h1' => 'H1 (largest)',
                                        'h2' => 'H2',
                                        'h3' => 'H3',
                                        'h4' => 'H4',
                                        'h5' => 'H5',
                                        'h6' => 'H6 (smallest)',
                                    ]),

                                ColorPicker::make('text_color')
                                    ->inlineLabel()
                                    ->label('Page Heading Color'),
                                ColorPicker::make('bg_color')
                                    ->inlineLabel()
                                    ->label('Header Footer table Color'),
                                ColorPicker::make('footer_color')
                                    ->inlineLabel()
                                    ->label('Table Text Color'),
                            ]),
					]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('invoiceLayout')
                    ->label('Invoice Layout')
                    ->icon('heroicon-o-document-text')
                    ->url(fn ($record) => static::getUrl('invoiceLayout', ['record' => $record]))
                    ->color('info'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            BanksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'view' => Pages\ViewCompany::route('/{record}'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
			'invoiceLayout' => Pages\EditCompanyInvoiceLayout::route('/{record}/invoice-layout'),

        ];
    }
}
