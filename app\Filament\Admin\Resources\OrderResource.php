<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\OrderResource\Pages;
use App\Filament\Admin\Resources\OrderResource\RelationManagers;
use App\Models\Company;
use App\Models\Currency;
use App\Models\Order;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, Group, Hidden, Placeholder, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'icon-receipt-cutoff';
	//menu navigation order
	protected static ?int $navigationSort = 0;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Hidden::make('order_create_by')
                    ->default(Auth::user()->id),
                Select::make('company_id')
                    ->label('Select Client')
                    ->helperText('Note: This will become the client in the invoice')
                    ->searchable()
                    ->columnSpan(6)
                    ->options(fn() => Company::where('type', 2)->pluck('name', 'id'))
					->suffixAction(
						ActionsAction::make('copyCostToPrice')
							->icon('heroicon-m-plus')
							->requiresConfirmation()
							->tooltip('Add New Company')
							->url('/admin/companies/create')
					),
                DatePicker::make('order_date')
                    ->columnSpan(2)
                    ->default(today())
                    ->timezone('Asia/Jakarta')
                    ->required()
                    ->native(false),
                Select::make('currency_id')
                    ->searchable()
                    ->label('Currency')
					->inlineLabel()
					->columnSpan(6)
					->columnStart(3)
                    ->required()
                    ->placeholder(null)
					->live(onBlur:true)
                    ->options(
                        Currency::get()
                            ->mapWithKeys(fn ($currency) => [
                                $currency->id => "{$currency->symbol} - {$currency->name}"
                            ])
                            ->toArray()
                    ),
				TextInput::make('order_amount')
					->numeric()
					->inlineLabel()
					->columnSpan(6)
					->columnStart(3)
					->required()
					->live(onBlur:true)
					->extraInputAttributes(['class'=>'text-end'])
					->prefix(fn ($get) => $get('currency_id') ? Currency::find($get('currency_id'))->symbol : '')
					->afterStateUpdated(function (callable $set, $state, callable $get) {
						if($state >= 100000) {
							$set('booking_fee', 100);
						} elseif($state >= 50000) {
							$set('booking_fee', 50);
						} else {
							$set('booking_fee', 30);
						}
						$set('total', $state + $get('booking_fee'));
					}),
					TextInput::make('booking_fee')
						->numeric()
						->inlineLabel()
						->prefix(fn ($get) => $get('currency_id') ? Currency::find($get('currency_id'))->symbol : '')
						->columnSpan(6)
						->columnStart(3)
						->live(onBlur:true)
						->extraInputAttributes(['class'=>'text-end'])
						->afterStateUpdated(function (callable $set, $state, callable $get) {
							$set('total', $state + $get('order_amount'));
						}),
					TextInput::make('rates')
						->numeric()
						->inlineLabel()
						->columnSpan(6)
						->columnStart(3)
						->prefix('IDR')
						->live(onBlur:true)
						->extraInputAttributes(['class'=>'text-end']),
                Placeholder::make('totalView')
                    ->label('Total Order')
                    ->content(function ($get) {
                        $currency = $get('currency_id');
						if($currency) {
							$currency = Currency::find($currency);
						}
                        $symbol = $currency?->symbol ?? '';
                        $total = number_format($get('total') ?? 0, 2, ',', '.');
                        $totalIdr = number_format(($get('total') ?? 0) * ($get('rates') ?? 0), 2, ',', '.');
                        return new HtmlString("<div>{$symbol} {$total}</div><div>IDR {$totalIdr}</div>");
                    })
                    ->inlineLabel()
                    ->extraAttributes(['class' => 'text-end font-bold'])
					->columnSpan(6)
					->columnStart(3),

                Hidden::make('total'),
                Hidden::make('status')->default('Draft'),
            ])
            ->columns(8);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([])
            ->filters([])
            ->actions([])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            // 'create' => Pages\CreateOrder::route('/create'),
            // 'view' => Pages\ViewOrder::route('/{record}'),
            // 'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }
}
