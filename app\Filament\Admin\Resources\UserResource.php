<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Spatie\Permission\Models\Role;

class UserResource extends Resource
{
    //Resource Settings
    protected static ?string $model = User::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
	protected static ?string $activeNavigationIcon = 'heroicon-s-users';
    protected static ?string $navigationGroup = 'Access Control';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (string $state, Forms\Set $set, ?string $old) {
                        // Only update if name is changed
                        if ($old !== $state) {
                            $username = strtolower(str_replace(' ', '_', $state));
                            $originalUsername = $username;
                            $counter = 1;
                            while (\App\Models\User::where('username', $username)
                                ->where('id', '!=', request()->route('record'))
                                ->exists()) {
                                $username = $originalUsername . '_' . $counter;
                                $counter++;
                            }
                            $set('username', $username);
                        }
                    }),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
				Forms\Components\TextInput::make('username')->required()
					->unique(ignoreRecord: true)
					->live()
                    ->maxLength(255)
                    ->autocomplete('new-username')
                    ->dehydrated(fn ($state) => filled($state)),
                Forms\Components\TextInput::make('password')
                    ->password()
                    ->required(fn ($context) => $context === 'create')
                    ->dehydrated(fn ($state) => filled($state))
                    ->maxLength(255),
                Forms\Components\Select::make('roles')
                    ->label('Roles')
                    ->multiple()
                    ->relationship('roles', 'name')
                    ->preload()
                    ->searchable(),
                Forms\Components\FileUpload::make('profile_photo_path')
                    ->label('Avatar')
                    ->image()
                    ->disk('public')
                    ->directory('avatars')
                    ->visibility('public')
                    ->imageEditor(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('profile_photo_path')
                    ->label('Avatar')
                    ->circular()
                    ->defaultImageUrl(function (User $record): string {
                        $name = $record->name;
                        $initials = collect(explode(' ', $name))->map(fn (string $segment): string => mb_substr($segment, 0, 1))->join('');
                        return 'https://ui-avatars.com/api/?name=' . urlencode($initials) . '&color=FFFFFF&background=111827';
                    }),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->label('Roles')
                    ->badge()
                    ->color(fn ($record) => $record->hasRole('Super Admin') ? 'danger' : 'primary')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->iconButton(),
                Tables\Actions\EditAction::make()->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
