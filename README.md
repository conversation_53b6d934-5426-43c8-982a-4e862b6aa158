# Laravel Starterkit Project

Proyek ini adalah implementasi dari <PERSON> Starterkit sesuai dengan arsitektur yang telah ditentukan.

## Stack Backend

- Laravel sebagai Backend Framework
- Livewire sebagai frontend framework untuk kegiatan administrasi dan pengelolaan data
- Laravel Sanctum untuk autentikasi API
- Laravel Pulse untuk monitoring aplikasi
- Laravel Scout untuk pencarian
- Spatie Role Permission untuk manajemen role dan permission
- Spatie Activity Log untuk logging aktivitas
- Spatie Media Library untuk manajemen media
- Spatie <PERSON> Health untuk monitoring kesehatan aplikasi
- Spatie Laravel Settings untuk manajemen pengaturan
- <PERSON><PERSON> Honey Pot untuk proteksi form
- <PERSON><PERSON> Schedule Monitor untuk monitoring jadwal
- Spatie Laravel Backup untuk backup
- MySQL sebagai database
- Laravel Cashier untuk manajemen pembayaran
- Laravel Cashier Paypal untuk integrasi dengan Paypal
- Scribe untuk dokumentasi API

## Stack Frontend

### Web Admin
- Livewire
- Alpine.js
- Tailwind CSS
- Flowbite

## Instalasi

1. Clone repository
```bash
git clone <repository-url>
```

2. Masuk ke direktori proyek
```bash
cd laravel-starterkit-project
```

3. Instal dependensi PHP
```bash
composer install
```

4. Instal dependensi JavaScript
```bash
npm install
```

5. Salin file .env.example ke .env
```bash
cp .env.example .env
```

6. Generate application key
```bash
php artisan key:generate
```

7. Konfigurasi database di file .env
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel_starterkit_project
DB_USERNAME=root
DB_PASSWORD=
```

8. Jalankan migrasi database
```bash
php artisan migrate
```

9. Jalankan seeder (opsional)
```bash
php artisan db:seed
```

10. Kompilasi aset frontend
```bash
npm run build
```

11. Jalankan server development
```bash
php artisan serve
```

## Fitur

- Autentikasi pengguna (login, register, reset password)
- Manajemen pengguna
- Manajemen role dan permission
- Manajemen tim
- Manajemen media
- Monitoring aplikasi
- Monitoring kesehatan aplikasi
- Monitoring jadwal
- Backup
- API dengan dokumentasi
- Integrasi pembayaran

## Lisensi

Proyek ini dilisensikan di bawah [MIT License](LICENSE).
