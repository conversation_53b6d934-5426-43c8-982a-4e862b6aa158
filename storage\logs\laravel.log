[2025-05-27 16:27:39] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-27 16:27:39] local.INFO: Section found {"section_id":1,"current_column_count":1,"current_layout":"equal"} 
[2025-05-27 16:27:39] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-27 16:27:39] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-27 16:27:39] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-27 16:27:41] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"custom","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-27 16:27:41] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"equal"} 
[2025-05-27 16:27:41] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"custom","column_widths":[],"background_color":"","min_height":50} 
[2025-05-27 16:27:41] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-27 16:27:41] local.INFO: Section after update {"column_count":2,"column_layout":"custom","column_widths":[]} 
[2025-05-27 16:27:48] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-27 16:27:48] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"custom"} 
[2025-05-27 16:27:48] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-27 16:27:48] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-27 16:27:48] local.INFO: Section after update {"column_count":2,"column_layout":"custom","column_widths":["20","80"]} 
[2025-05-27 16:27:48] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-27 16:27:48] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"custom"} 
[2025-05-27 16:27:48] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-27 16:27:48] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-27 16:27:48] local.INFO: Section after update {"column_count":2,"column_layout":"custom","column_widths":["20","80"]} 
[2025-05-27 16:27:48] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-27 16:27:48] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"custom"} 
[2025-05-27 16:27:48] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-27 16:27:48] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-27 16:27:48] local.INFO: Section after update {"column_count":2,"column_layout":"custom","column_widths":["20","80"]} 
[2025-05-27 16:48:56] local.INFO: Auto-saving section settings {"selectedElementId":2,"sectionSettings":{"section_name":"","column_count":"4","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-27 16:48:56] local.INFO: Section found {"section_id":2,"current_column_count":1,"current_layout":"equal"} 
[2025-05-27 16:48:56] local.INFO: Prepared update data {"section_name":"","column_count":4,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-27 16:48:56] local.INFO: Update result {"success":true,"section_id":2} 
[2025-05-27 16:48:56] local.INFO: Section after update {"column_count":4,"column_layout":"equal","column_widths":[]} 
[2025-05-27 16:49:02] local.INFO: Auto-saving section settings {"selectedElementId":2,"sectionSettings":{"section_name":"","column_count":"3","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-27 16:49:02] local.INFO: Section found {"section_id":2,"current_column_count":4,"current_layout":"equal"} 
[2025-05-27 16:49:02] local.INFO: Prepared update data {"section_name":"","column_count":3,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-27 16:49:02] local.INFO: Update result {"success":true,"section_id":2} 
[2025-05-27 16:49:02] local.INFO: Section after update {"column_count":3,"column_layout":"equal","column_widths":[]} 
[2025-05-27 16:50:53] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-27 16:50:53] local.INFO: Section found {"section_id":4,"current_column_count":1,"current_layout":"equal"} 
[2025-05-27 16:50:53] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-27 16:50:53] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-27 16:50:53] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-27 16:52:10] local.INFO: Auto-saving section settings {"selectedElementId":5,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-27 16:52:10] local.INFO: Section found {"section_id":5,"current_column_count":1,"current_layout":"equal"} 
[2025-05-27 16:52:10] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-27 16:52:10] local.INFO: Update result {"success":true,"section_id":5} 
[2025-05-27 16:52:10] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:03:28] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `sessions` where `id` = ij53A8aENQvhDoREAh6mE4c3GpbGOtUfYzepiO4c limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `sessions` where `id` = ij53A8aENQvhDoREAh6mE4c3GpbGOtUfYzepiO4c limit 1) at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('ij53A8aENQvhDoR...')
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('ij53A8aENQvhDoR...')
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#14 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#57 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getPdo()
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('ij53A8aENQvhDoR...')
#21 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('ij53A8aENQvhDoR...')
#22 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#23 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#66 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#67 {main}
"} 
[2025-05-28 14:03:58] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 {main}
"} 
[2025-05-28 14:09:55] local.INFO: Auto-saving section settings {"selectedElementId":5,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":"2"}} 
[2025-05-28 14:09:55] local.INFO: Section found {"section_id":5,"current_column_count":2,"current_layout":"equal"} 
[2025-05-28 14:09:55] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":2} 
[2025-05-28 14:09:55] local.INFO: Update result {"success":true,"section_id":5} 
[2025-05-28 14:09:55] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:09:56] local.INFO: Auto-saving section settings {"selectedElementId":5,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":"20"}} 
[2025-05-28 14:09:56] local.INFO: Section found {"section_id":5,"current_column_count":2,"current_layout":"equal"} 
[2025-05-28 14:09:56] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":20} 
[2025-05-28 14:09:56] local.INFO: Update result {"success":true,"section_id":5} 
[2025-05-28 14:09:56] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:10:19] local.INFO: Auto-saving section settings {"selectedElementId":3,"sectionSettings":{"section_name":":","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:10:19] local.INFO: Section found {"section_id":3,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:10:19] local.INFO: Prepared update data {"section_name":":","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:10:19] local.INFO: Update result {"success":true,"section_id":3} 
[2025-05-28 14:10:19] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:10:20] local.INFO: Auto-saving section settings {"selectedElementId":3,"sectionSettings":{"section_name":":INE","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:10:20] local.INFO: Section found {"section_id":3,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:10:20] local.INFO: Prepared update data {"section_name":":INE","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:10:20] local.INFO: Update result {"success":true,"section_id":3} 
[2025-05-28 14:10:20] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:10:21] local.INFO: Auto-saving section settings {"selectedElementId":3,"sectionSettings":{"section_name":"L","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:10:21] local.INFO: Section found {"section_id":3,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:10:21] local.INFO: Prepared update data {"section_name":"L","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:10:21] local.INFO: Update result {"success":true,"section_id":3} 
[2025-05-28 14:10:21] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:10:22] local.INFO: Auto-saving section settings {"selectedElementId":3,"sectionSettings":{"section_name":"Line","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:10:22] local.INFO: Section found {"section_id":3,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:10:22] local.INFO: Prepared update data {"section_name":"Line","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:10:22] local.INFO: Update result {"success":true,"section_id":3} 
[2025-05-28 14:10:22] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:10:25] local.INFO: Auto-saving section settings {"selectedElementId":3,"sectionSettings":{"section_name":"Line","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":"2"}} 
[2025-05-28 14:10:25] local.INFO: Section found {"section_id":3,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:10:25] local.INFO: Prepared update data {"section_name":"Line","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":2} 
[2025-05-28 14:10:25] local.INFO: Update result {"success":true,"section_id":3} 
[2025-05-28 14:10:25] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:10:26] local.INFO: Auto-saving section settings {"selectedElementId":3,"sectionSettings":{"section_name":"Line","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":"20"}} 
[2025-05-28 14:10:26] local.INFO: Section found {"section_id":3,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:10:26] local.INFO: Prepared update data {"section_name":"Line","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":20} 
[2025-05-28 14:10:26] local.INFO: Update result {"success":true,"section_id":3} 
[2025-05-28 14:10:26] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:24:46] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"1","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:24:46] local.INFO: Section found {"section_id":4,"current_column_count":2,"current_layout":"equal"} 
[2025-05-28 14:24:46] local.INFO: Prepared update data {"section_name":"","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:24:46] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-28 14:24:46] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:24:53] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:24:53] local.INFO: Section found {"section_id":4,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:24:53] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:24:53] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-28 14:24:53] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:24:57] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"1","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:24:57] local.INFO: Section found {"section_id":4,"current_column_count":2,"current_layout":"equal"} 
[2025-05-28 14:24:57] local.INFO: Prepared update data {"section_name":"","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:24:57] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-28 14:24:57] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:25:07] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:25:07] local.INFO: Section found {"section_id":4,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:25:07] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:25:07] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-28 14:25:07] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:25:11] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"1","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:25:11] local.INFO: Section found {"section_id":4,"current_column_count":2,"current_layout":"equal"} 
[2025-05-28 14:25:11] local.INFO: Prepared update data {"section_name":"","column_count":1,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:25:11] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-28 14:25:11] local.INFO: Section after update {"column_count":1,"column_layout":"equal","column_widths":[]} 
[2025-05-28 14:25:27] local.INFO: Auto-saving section settings {"selectedElementId":4,"sectionSettings":{"section_name":"","column_count":"2","column_layout":"equal","column_widths":[],"background_color":"","min_height":50}} 
[2025-05-28 14:25:27] local.INFO: Section found {"section_id":4,"current_column_count":1,"current_layout":"equal"} 
[2025-05-28 14:25:27] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":[],"background_color":"","min_height":50} 
[2025-05-28 14:25:27] local.INFO: Update result {"success":true,"section_id":4} 
[2025-05-28 14:25:27] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":[]} 
[2025-05-28 15:17:22] local.ERROR: Unable to call component method. Public method [loadBlockSettings] not found on component {"userId":1,"exception":"[object] (Livewire\\Exceptions\\MethodNotFoundException(code: 0): Unable to call component method. Public method [loadBlockSettings] not found on component at D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php:470)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Admin\\Pages\\VisualTemplateBuilder), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#1 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#2 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#52 {main}
"} 
[2025-05-28 15:28:15] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"equal","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-28 15:28:15] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"custom"} 
[2025-05-28 15:28:15] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"equal","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-28 15:28:15] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-28 15:28:15] local.INFO: Section after update {"column_count":2,"column_layout":"equal","column_widths":["20","80"]} 
[2025-05-28 15:28:20] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"flex","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-28 15:28:20] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"equal"} 
[2025-05-28 15:28:20] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"flex","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-28 15:28:20] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-28 15:28:20] local.INFO: Section after update {"column_count":2,"column_layout":"flex","column_widths":["20","80"]} 
[2025-05-28 15:28:23] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-28 15:28:23] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"flex"} 
[2025-05-28 15:28:23] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-28 15:28:23] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-28 15:28:23] local.INFO: Section after update {"column_count":2,"column_layout":"custom","column_widths":["20","80"]} 
[2025-05-28 15:35:19] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"flex","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-28 15:35:19] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"custom"} 
[2025-05-28 15:35:19] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"flex","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-28 15:35:19] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-28 15:35:19] local.INFO: Section after update {"column_count":2,"column_layout":"flex","column_widths":["20","80"]} 
[2025-05-28 15:37:51] local.INFO: Auto-saving section settings {"selectedElementId":1,"sectionSettings":{"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50}} 
[2025-05-28 15:37:51] local.INFO: Section found {"section_id":1,"current_column_count":2,"current_layout":"flex"} 
[2025-05-28 15:37:51] local.INFO: Prepared update data {"section_name":"","column_count":2,"column_layout":"custom","column_widths":["20","80"],"background_color":"#ffffff","min_height":50} 
[2025-05-28 15:37:51] local.INFO: Update result {"success":true,"section_id":1} 
[2025-05-28 15:37:51] local.INFO: Section after update {"column_count":2,"column_layout":"custom","column_widths":["20","80"]} 
[2025-05-28 15:46:42] local.INFO: Building inline styles for block {"block_id":5,"block_type":"company_logo","configuration":{"alignment":"center","font_size":16,"font_color":"#ff0000","font_weight":"bold"},"content_data":{"width":"100px","height":"auto","alt_text":"Company Logo","field_path":"company.logo","storage_prefix":"storage/"}} 
[2025-05-28 15:46:42] local.INFO: Generated styles {"block_id":5,"styles":["text-align: center","font-size: 16pt","font-weight: bold","color: #ff0000"]} 
[2025-05-28 15:46:42] local.INFO: Building inline styles for block {"block_id":5,"block_type":"company_logo","configuration":{"alignment":"center","font_size":16,"font_color":"#ff0000","font_weight":"bold"},"content_data":{"width":"150px","height":"100px","alt_text":"Test Logo","field_path":"company.logo","object_fit":"cover","storage_prefix":"storage/"}} 
[2025-05-28 15:46:42] local.INFO: Generated styles {"block_id":5,"styles":["text-align: center","font-size: 16pt","font-weight: bold","color: #ff0000"]} 
[2025-05-28 15:46:42] local.INFO: Building inline styles for block {"block_id":5,"block_type":"company_logo","configuration":{"alignment":"center","font_size":16,"font_color":"#ff0000","font_weight":"bold"},"content_data":{"width":"150px","height":"100px","alt_text":"Test Logo","field_path":"company.logo","object_fit":"cover","storage_prefix":"storage/"}} 
[2025-05-28 15:46:42] local.INFO: Generated styles {"block_id":5,"styles":["text-align: center","font-size: 16pt","font-weight: bold","color: #ff0000"]} 
[2025-05-28 15:46:42] local.INFO: Building inline styles for block {"block_id":2,"block_type":"company_name","configuration":[],"content_data":{"field_path":"company.name","default_color":"#000000","default_heading":"h3","text_color_path":"company.text_color","heading_size_path":"company.heading_size"}} 
[2025-05-28 15:46:42] local.INFO: Generated styles {"block_id":2,"styles":[]} 
[2025-05-28 15:46:42] local.INFO: Building inline styles for block {"block_id":26,"block_type":"company_contact_combined","configuration":[],"content_data":{"fields":{"fax":{"label":"Fax","order":5,"format":"text","prefix":"Fax: ","field_path":"company.fax","show_label":false},"email":{"label":"Email","order":3,"format":"email","prefix":"Email: ","field_path":"company.email","show_label":false},"phone":{"label":"Phone","order":2,"format":"text","prefix":"Phone: ","field_path":"company.phone","show_label":false},"address":{"label":"Address","order":1,"format":"html","field_path":"company.address","show_label":false},"website":{"label":"Website","order":4,"format":"url","prefix":"Website: ","field_path":"company.website","show_label":false}},"layout":"vertical","spacing":"compact"}} 
[2025-05-28 15:46:42] local.INFO: Generated styles {"block_id":26,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":5,"block_type":"company_logo","configuration":{"alignment":"center","font_size":16,"font_color":"#ff0000","font_weight":"bold"},"content_data":{"width":"150px","height":"100px","alt_text":"Test Logo","field_path":"company.logo","object_fit":"cover","storage_prefix":"storage/"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":5,"styles":["text-align: center","font-size: 16pt","font-weight: bold","color: #ff0000"]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":2,"block_type":"company_name","configuration":[],"content_data":{"field_path":"company.name","default_color":"#000000","default_heading":"h3","text_color_path":"company.text_color","heading_size_path":"company.heading_size"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":2,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":26,"block_type":"company_contact_combined","configuration":[],"content_data":{"fields":{"fax":{"label":"Fax","order":5,"format":"text","prefix":"Fax: ","field_path":"company.fax","show_label":false},"email":{"label":"Email","order":3,"format":"email","prefix":"Email: ","field_path":"company.email","show_label":false},"phone":{"label":"Phone","order":2,"format":"text","prefix":"Phone: ","field_path":"company.phone","show_label":false},"address":{"label":"Address","order":1,"format":"html","field_path":"company.address","show_label":false},"website":{"label":"Website","order":4,"format":"url","prefix":"Website: ","field_path":"company.website","show_label":false}},"layout":"vertical","spacing":"compact"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":26,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":10,"block_type":"horizontal_line","configuration":[],"content_data":{"color":"#cccccc","style":"solid","width":"100%","thickness":"1px"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":10,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":17,"block_type":"horizontal_line","configuration":[],"content_data":{"color":"#cccccc","style":"solid","width":"100%","thickness":"1px"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":17,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":14,"block_type":"invoice_number","configuration":[],"content_data":{"prefix":"Invoice No: ","suffix":"","field_path":"invoice.invoice_no"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":14,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":20,"block_type":"invoice_due_date","configuration":[],"content_data":{"prefix":"Due Date: ","suffix":"","field_path":"invoice.invoice_due","date_format":"d/m/Y"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":20,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":19,"block_type":"invoice_date","configuration":[],"content_data":{"prefix":"Invoice Date: ","suffix":"","field_path":"invoice.invoice_date","date_format":"d/m/Y"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":19,"styles":[]} 
[2025-05-28 15:46:47] local.INFO: Building inline styles for block {"block_id":18,"block_type":"customer_name","configuration":[],"content_data":{"prefix":"Bill to: ","suffix":"","field_path":"customer.name"}} 
[2025-05-28 15:46:47] local.INFO: Generated styles {"block_id":18,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":5,"block_type":"company_logo","configuration":{"alignment":"center","font_size":16,"font_color":"#ff0000","font_weight":"bold"},"content_data":{"width":"150px","height":"100px","alt_text":"Test Logo","field_path":"company.logo","object_fit":"cover","storage_prefix":"storage/"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":5,"styles":["text-align: center","font-size: 16pt","font-weight: bold","color: #ff0000"]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":2,"block_type":"company_name","configuration":[],"content_data":{"field_path":"company.name","default_color":"#000000","default_heading":"h3","text_color_path":"company.text_color","heading_size_path":"company.heading_size"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":2,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":26,"block_type":"company_contact_combined","configuration":[],"content_data":{"fields":{"fax":{"label":"Fax","order":5,"format":"text","prefix":"Fax: ","field_path":"company.fax","show_label":false},"email":{"label":"Email","order":3,"format":"email","prefix":"Email: ","field_path":"company.email","show_label":false},"phone":{"label":"Phone","order":2,"format":"text","prefix":"Phone: ","field_path":"company.phone","show_label":false},"address":{"label":"Address","order":1,"format":"html","field_path":"company.address","show_label":false},"website":{"label":"Website","order":4,"format":"url","prefix":"Website: ","field_path":"company.website","show_label":false}},"layout":"vertical","spacing":"compact"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":26,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":10,"block_type":"horizontal_line","configuration":[],"content_data":{"color":"#cccccc","style":"solid","width":"100%","thickness":"1px"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":10,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":17,"block_type":"horizontal_line","configuration":[],"content_data":{"color":"#cccccc","style":"solid","width":"100%","thickness":"1px"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":17,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":14,"block_type":"invoice_number","configuration":[],"content_data":{"prefix":"Invoice No: ","suffix":"","field_path":"invoice.invoice_no"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":14,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":20,"block_type":"invoice_due_date","configuration":[],"content_data":{"prefix":"Due Date: ","suffix":"","field_path":"invoice.invoice_due","date_format":"d/m/Y"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":20,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":19,"block_type":"invoice_date","configuration":[],"content_data":{"prefix":"Invoice Date: ","suffix":"","field_path":"invoice.invoice_date","date_format":"d/m/Y"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":19,"styles":[]} 
[2025-05-28 15:47:24] local.INFO: Building inline styles for block {"block_id":18,"block_type":"customer_name","configuration":[],"content_data":{"prefix":"Bill to: ","suffix":"","field_path":"customer.name"}} 
[2025-05-28 15:47:24] local.INFO: Generated styles {"block_id":18,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":5,"block_type":"company_logo","configuration":{"alignment":"center","font_size":16,"font_color":"#ff0000","font_weight":"bold"},"content_data":{"width":"150px","height":"100px","alt_text":"Test Logo","field_path":"company.logo","object_fit":"cover","storage_prefix":"storage/"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":5,"styles":["text-align: center","font-size: 16pt","font-weight: bold","color: #ff0000"]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":2,"block_type":"company_name","configuration":[],"content_data":{"field_path":"company.name","default_color":"#000000","default_heading":"h3","text_color_path":"company.text_color","heading_size_path":"company.heading_size"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":2,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":26,"block_type":"company_contact_combined","configuration":[],"content_data":{"fields":{"fax":{"label":"Fax","order":5,"format":"text","prefix":"Fax: ","field_path":"company.fax","show_label":false},"email":{"label":"Email","order":3,"format":"email","prefix":"Email: ","field_path":"company.email","show_label":false},"phone":{"label":"Phone","order":2,"format":"text","prefix":"Phone: ","field_path":"company.phone","show_label":false},"address":{"label":"Address","order":1,"format":"html","field_path":"company.address","show_label":false},"website":{"label":"Website","order":4,"format":"url","prefix":"Website: ","field_path":"company.website","show_label":false}},"layout":"vertical","spacing":"compact"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":26,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":10,"block_type":"horizontal_line","configuration":[],"content_data":{"color":"#cccccc","style":"solid","width":"100%","thickness":"1px"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":10,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":17,"block_type":"horizontal_line","configuration":[],"content_data":{"color":"#cccccc","style":"solid","width":"100%","thickness":"1px"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":17,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":14,"block_type":"invoice_number","configuration":[],"content_data":{"prefix":"Invoice No: ","suffix":"","field_path":"invoice.invoice_no"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":14,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":20,"block_type":"invoice_due_date","configuration":[],"content_data":{"prefix":"Due Date: ","suffix":"","field_path":"invoice.invoice_due","date_format":"d/m/Y"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":20,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":19,"block_type":"invoice_date","configuration":[],"content_data":{"prefix":"Invoice Date: ","suffix":"","field_path":"invoice.invoice_date","date_format":"d/m/Y"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":19,"styles":[]} 
[2025-05-28 15:47:45] local.INFO: Building inline styles for block {"block_id":18,"block_type":"customer_name","configuration":[],"content_data":{"prefix":"Bill to: ","suffix":"","field_path":"customer.name"}} 
[2025-05-28 15:47:45] local.INFO: Generated styles {"block_id":18,"styles":[]} 
[2025-05-28 16:00:03] local.DEBUG: From: New Mini App <<EMAIL>>
To: <EMAIL>
Subject: Some of the health checks on New Mini App have failed
MIME-Version: 1.0
Date: Wed, 28 May 2025 16:00:03 +0000
Message-ID: <<EMAIL>>
Content-Type: multipart/alternative; boundary=k13TvPbJ

--k13TvPbJ
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: quoted-printable

New Mini App: https://web_starter.test

# Laravel Health

The following checks reported warnings and errors:

- Optimized App: Configs are not cached.

© 2025 New Mini App. All rights reserved.

--k13TvPbJ
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>New Mini App</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="color-scheme" content="light">
<meta name="supported-color-schemes" content="light">
<style>
@media only screen and (max-width: 600px) {
.inner-body {
width: 100% !important;
}

.footer {
width: 100% !important;
}
}

@media only screen and (max-width: 500px) {
.button {
width: 100% !important;
}
}
</style>

</head>
<body style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -webkit-text-size-adjust: none; background-color: #ffffff; color: #718096; height: 100%; line-height: 1.4; margin: 0; padding: 0; width: 100% !important;">

<table class="wrapper" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #edf2f7; margin: 0; padding: 0; width: 100%;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table class="content" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 0; padding: 0; width: 100%;">
<tr>
<td class="header" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; padding: 25px 0; text-align: center;">
<a href="https://web_starter.test" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3d4852; font-size: 19px; font-weight: bold; text-decoration: none; display: inline-block;">
New Mini App
</a>
</td>
</tr>

<!-- Email Body -->
<tr>
<td class="body" width="100%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #edf2f7; border-bottom: 1px solid #edf2f7; border-top: 1px solid #edf2f7; margin: 0; padding: 0; width: 100%; border: hidden !important;">
<table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px; background-color: #ffffff; border-color: #e8e5ef; border-radius: 2px; border-width: 1px; box-shadow: 0 2px 0 rgba(0, 0, 150, 0.025), 2px 4px 0 rgba(0, 0, 150, 0.015); margin: 0 auto; padding: 0; width: 570px;">
<!-- Body content -->
<tr>
<td class="content-cell" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw; padding: 32px;">
<h1 style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3d4852; font-size: 18px; font-weight: bold; margin-top: 0; text-align: left;">Laravel Health</h1>
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">The following checks reported warnings and errors:</p>
<ul style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; line-height: 1.4; text-align: left;">
<li style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">Optimized App: Configs are not cached.</li>
</ul>



</td>
</tr>
</table>
</td>
</tr>

<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table class="footer" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px; margin: 0 auto; padding: 0; text-align: center; width: 570px;">
<tr>
<td class="content-cell" align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw; padding: 32px;">
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; line-height: 1.5em; margin-top: 0; color: #b0adc5; font-size: 12px; text-align: center;">© 2025 New Mini App. All rights reserved.</p>

</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</body>
</html>
--k13TvPbJ--
  
[2025-05-28 16:19:13] local.ERROR: Class "App\Filament\Admin\Resources\TextInput" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Admin\\Resources\\TextInput\" not found at D:\\sites\\starterkit\\web_starter\\app\\Filament\\Admin\\Resources\\FontResource.php:27)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(377): App\\Filament\\Admin\\Resources\\FontResource::form(Object(Filament\\Forms\\Form))
#1 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(338): Filament\\Resources\\Pages\\EditRecord->getForms()
#2 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(409): Filament\\Pages\\BasePage->cacheForms()
#3 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(400): Filament\\Pages\\BasePage->getCachedForms()
#4 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\support\\src\\Concerns\\ResolvesDynamicLivewireProperties.php(27): Filament\\Pages\\BasePage->getForm('form')
#5 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(107): Filament\\Pages\\BasePage->__get('form')
#6 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(90): Filament\\Resources\\Pages\\EditRecord->fillFormWithDataAndCallHooks(Object(App\\Models\\Font))
#7 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(77): Filament\\Resources\\Pages\\EditRecord->fillForm()
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Resources\\Pages\\EditRecord->mount('1')
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#13 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#14 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#15 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#16 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#17 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), Array, NULL, false)
#18 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), Array, NULL, false)
#19 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), Array, NULL, false)
#20 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#21 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#22 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#23 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('1')
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), '__invoke')
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#81 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#82 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#83 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#84 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#85 {main}
"} 
[2025-05-28 16:19:29] local.ERROR: Method Filament\Forms\Components\Textarea::url does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method Filament\\Forms\\Components\\Textarea::url does not exist. at D:\\sites\\starterkit\\web_starter\\vendor\\filament\\support\\src\\Concerns\\Macroable.php:77)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\app\\Filament\\Admin\\Resources\\FontResource.php(31): Filament\\Support\\Components\\Component->__call('url', Array)
#1 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(377): App\\Filament\\Admin\\Resources\\FontResource::form(Object(Filament\\Forms\\Form))
#2 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(338): Filament\\Resources\\Pages\\EditRecord->getForms()
#3 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(409): Filament\\Pages\\BasePage->cacheForms()
#4 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(400): Filament\\Pages\\BasePage->getCachedForms()
#5 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\support\\src\\Concerns\\ResolvesDynamicLivewireProperties.php(27): Filament\\Pages\\BasePage->getForm('form')
#6 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(107): Filament\\Pages\\BasePage->__get('form')
#7 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(90): Filament\\Resources\\Pages\\EditRecord->fillFormWithDataAndCallHooks(Object(App\\Models\\Font))
#8 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php(77): Filament\\Resources\\Pages\\EditRecord->fillForm()
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Resources\\Pages\\EditRecord->mount('1')
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#14 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#15 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#16 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#17 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#18 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), Array, NULL, false)
#19 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), Array, NULL, false)
#20 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), Array, NULL, false)
#21 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#22 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#23 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#24 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('1')
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\FontResource\\Pages\\EditFont), '__invoke')
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#85 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#86 {main}
"} 
[2025-05-28 16:50:54] local.ERROR: Command "make:services" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:filament-cluster
    make:filament-exporter
    make:filament-importer
    make:filament-issue
    make:filament-page
    make:filament-panel
    make:filament-relation-manager
    make:filament-resource
    make:filament-theme
    make:filament-user
    make:filament-widget
    make:form-field
    make:form-layout
    make:infolist-entry
    make:infolist-layout
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:livewire
    make:livewire-form
    make:livewire-table
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:setting
    make:settings-migration
    make:table-column
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:services\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:filament-cluster
    make:filament-exporter
    make:filament-importer
    make:filament-issue
    make:filament-page
    make:filament-panel
    make:filament-relation-manager
    make:filament-resource
    make:filament-theme
    make:filament-user
    make:filament-widget
    make:form-field
    make:form-layout
    make:infolist-entry
    make:infolist-layout
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:livewire
    make:livewire-form
    make:livewire-table
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:setting
    make:settings-migration
    make:table-column
    make:test
    make:trait
    make:view at D:\\sites\\starterkit\\web_starter\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('make:services')
#1 D:\\sites\\starterkit\\web_starter\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\sites\\starterkit\\web_starter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-05-29 06:44:47] local.ERROR: Class "App\Filament\Admin\Resources\CompanyResource\Pages\EditCompanyInvoiceLayout" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout\" not found at D:\\sites\\starterkit\\web_starter\\app\\Filament\\Admin\\Resources\\CompanyResource.php:288)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Admin\\Resources\\CompanyResource::getPages()
#1 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#14 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('D:\\\\sites\\\\starte...')
#28 D:\\sites\\starterkit\\web_starter\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('D:\\\\sites\\\\starte...')
#29 D:\\sites\\starterkit\\web_starter\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#47 {main}
"} 
[2025-05-29 06:56:50] local.ERROR: Call to undefined relationship [invoiceLayout] on model [App\Models\Company]. {"userId":1,"exception":"[object] (Illuminate\\Database\\Eloquent\\RelationNotFoundException(code: 0): Call to undefined relationship [invoiceLayout] on model [App\\Models\\Company]. at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\RelationNotFoundException.php:35)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(938): Illuminate\\Database\\Eloquent\\RelationNotFoundException::make(Object(App\\Models\\Company), 'invoiceLayout')
#1 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(119): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(934): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(908): Illuminate\\Database\\Eloquent\\Builder->getRelation('invoiceLayout')
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'invoiceLayout', Object(Closure))
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(537): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(584): Illuminate\\Database\\Eloquent\\Builder->find('15', Array)
#9 D:\\sites\\starterkit\\web_starter\\app\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout.php(19): Illuminate\\Database\\Eloquent\\Builder->findOrFail('15')
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout->mount('15')
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#15 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#16 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#17 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#18 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#19 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#20 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#21 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#22 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#23 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#24 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#25 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('15')
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '__invoke')
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#83 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#84 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#85 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#86 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#87 {main}
"} 
[2025-05-29 06:57:05] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'web_starter_kit.invoice_layouts' doesn't exist (Connection: mysql, SQL: select * from `invoice_layouts` where `invoice_layouts`.`company_id` in (15)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'web_starter_kit.invoice_layouts' doesn't exist (Connection: mysql, SQL: select * from `invoice_layouts` where `invoice_layouts`.`company_id` in (15)) at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(871): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(853): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(212): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(175): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(919): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'invoiceLayout', Object(Closure))
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#14 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(537): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(584): Illuminate\\Database\\Eloquent\\Builder->find('15', Array)
#16 D:\\sites\\starterkit\\web_starter\\app\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout.php(19): Illuminate\\Database\\Eloquent\\Builder->findOrFail('15')
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout->mount('15')
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#22 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#23 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#24 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#25 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#26 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#27 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#28 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#29 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#30 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#31 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#32 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('15')
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '__invoke')
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#90 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#91 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#92 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#93 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#94 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'web_starter_kit.invoice_layouts' doesn't exist at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(871): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(853): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(212): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(175): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(919): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(888): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'invoiceLayout', Object(Closure))
#14 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(537): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(584): Illuminate\\Database\\Eloquent\\Builder->find('15', Array)
#18 D:\\sites\\starterkit\\web_starter\\app\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout.php(19): Illuminate\\Database\\Eloquent\\Builder->findOrFail('15')
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout->mount('15')
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#24 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#25 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#26 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#27 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#28 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#29 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#30 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Array, NULL, false)
#31 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#32 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#33 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#34 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('15')
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '__invoke')
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#93 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#94 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#95 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#96 {main}
"} 
[2025-05-29 06:57:22] local.ERROR: Property [sections] does not exist on this collection instance. (View: D:\sites\starterkit\web_starter\resources\views\filament\admin\resources\company-resource\pages\edit-company-invoice-layout.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Property [sections] does not exist on this collection instance. (View: D:\\sites\\starterkit\\web_starter\\resources\\views\\filament\\admin\\resources\\company-resource\\pages\\edit-company-invoice-layout.blade.php) at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php:1045)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Exception), 1)
#1 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Exception), 1)
#2 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\sites\\\\starte...', Array)
#3 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\sites\\\\starte...', Array)
#4 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\sites\\\\starte...', Array)
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Object(Closure))
#10 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '<div></div>')
#11 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#12 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#13 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#14 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#15 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('15')
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '__invoke')
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#76 {main}

[previous exception] [object] (Exception(code: 0): Property [sections] does not exist on this collection instance. at D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php:1045)
[stacktrace]
#0 D:\\sites\\starterkit\\web_starter\\storage\\framework\\views\\ecaae1edb62b3a708a4dd8b001b54c51.php(14): Illuminate\\Support\\Collection->__get('sections')
#1 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('D:\\\\sites\\\\starte...')
#2 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#3 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\sites\\\\starte...', Array)
#4 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\sites\\\\starte...', Array)
#5 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\sites\\\\starte...', Array)
#6 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#9 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#10 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), Object(Closure))
#11 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '<div></div>')
#12 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Ad...', Array, NULL)
#13 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Ad...', Array)
#14 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#15 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#16 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('15')
#17 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout), '__invoke')
#18 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\sites\\starterkit\\web_starter\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#44 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\sites\\starterkit\\web_starter\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 D:\\sites\\starterkit\\web_starter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 D:\\sites\\starterkit\\web_starter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#76 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('D:\\\\sites\\\\starte...')
#77 {main}
"} 
