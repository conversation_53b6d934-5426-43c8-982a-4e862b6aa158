@props(['invoice'])

@php
    $invoiceDetails = $invoice->invoiceDetails ?? collect();
    $currency = $invoice->currency;
    $currencySymbol = $currency->symbol ?? '$';
    $currencyCode = $currency->code ?? 'USD';
@endphp

<div class="overflow-hidden">
    @if($invoiceDetails->count() > 0)
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-200 rounded-lg">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="border border-gray-200 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Description
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                            Qty
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                            Unit
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                            Price
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                            Subtotal
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($invoiceDetails as $detail)
                        <tr class="hover:bg-gray-50">
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                                <div class="max-w-xs">
                                    {!! $detail->description !!}
                                </div>
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900 text-center">
                                {{ number_format($detail->quantity, 2) }}
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900 text-center">
                                {{ $detail->unit ?? '-' }}
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900 text-right">
                                {{ $currencySymbol }} {{ number_format($detail->price, 2) }}
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm font-medium text-gray-900 text-right">
                                {{ $currencySymbol }} {{ number_format($detail->sub_total, 2) }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr class="bg-gray-50">
                        <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-medium text-gray-900 text-right">
                            <strong>Total:</strong>
                        </td>
                        <td class="border border-gray-200 px-4 py-3 text-sm font-bold text-gray-900 text-right bg-blue-50">
                            {{ $currencySymbol }} {{ number_format($invoiceDetails->sum('sub_total'), 2) }}
                        </td>
                    </tr>
                    @if($invoice->booking_fee > 0)
                        <tr class="bg-gray-50">
                            <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-medium text-gray-900 text-right">
                                <strong>Booking Fee:</strong>
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm font-medium text-gray-900 text-right">
                                {{ $currencySymbol }} {{ number_format($invoice->booking_fee, 2) }}
                            </td>
                        </tr>
                    @endif
                    <tr class="bg-blue-50">
                        <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-bold text-gray-900 text-right">
                            <strong>Invoice Total:</strong>
                        </td>
                        <td class="border border-gray-200 px-4 py-3 text-lg font-bold text-blue-900 text-right">
                            {{ $currencySymbol }} {{ number_format($invoice->invoice_amount, 2) }}
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>

        {{-- Summary Information --}}
        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Summary</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Items:</span>
                        <span class="font-medium">{{ $invoiceDetails->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Quantity:</span>
                        <span class="font-medium">{{ number_format($invoiceDetails->sum('quantity'), 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal:</span>
                        <span class="font-medium">{{ $currencySymbol }} {{ number_format($invoiceDetails->sum('sub_total'), 2) }}</span>
                    </div>
                    @if($invoice->booking_fee > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Booking Fee:</span>
                            <span class="font-medium">{{ $currencySymbol }} {{ number_format($invoice->booking_fee, 2) }}</span>
                        </div>
                    @endif
                    <div class="flex justify-between pt-2 border-t border-gray-200">
                        <span class="text-gray-900 font-semibold">Invoice Total:</span>
                        <span class="font-bold text-blue-600">{{ $currencySymbol }} {{ number_format($invoice->invoice_amount, 2) }}</span>
                    </div>
                </div>
            </div>

            @if($invoice->amount_inword)
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Amount in Words</h4>
                    <p class="text-sm text-gray-700 italic">
                        {{ $invoice->amount_inword }}
                    </p>
                </div>
            @endif
        </div>
    @else
        <div class="text-center py-8">
            <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No invoice details</h3>
            <p class="mt-1 text-sm text-gray-500">This invoice doesn't have any line items yet.</p>
        </div>
    @endif
</div>
