<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Resources\Pages\Page;
use App\Models\Company;

class EditCompanyInvoiceLayout extends Page
{
    protected static string $resource = CompanyResource::class;

    protected static string $view = 'filament.admin.resources.company-resource.pages.edit-company-invoice-layout';

    public Company $company;

    public function mount($record): void
    {
        $this->company = Company::with('invoiceLayout.sections')->findOrFail($record);

        if (!$this->company->invoiceLayout) {
            // Create default layout
            $layout = $this->company->invoiceLayout()->create([
                'name' => 'Default Layout',
            ]);

            // Create default sections
            $this->createDefaultSections($layout);

            $this->company->refresh();
            $this->company->load('invoiceLayout.sections');
        } else {
            // Ensure all basic sections exist
            $this->ensureBasicSections($this->company->invoiceLayout);
        }
    }

    protected function createDefaultSections($layout)
    {
        $sections = [
            'header' => $this->getDefaultHeaderGrid(),
            'body' => $this->getDefaultBodyGrid(),
            'footer' => $this->getDefaultFooterGrid(),
        ];

        foreach ($sections as $type => $grid) {
            $layout->sections()->create([
                'type' => $type,
                'style' => [],
                'layout' => $grid
            ]);
        }
    }

    protected function ensureBasicSections($layout)
    {
        $basicSections = ['header', 'body', 'footer'];

        foreach ($basicSections as $sectionType) {
            if (!$layout->sections()->where('type', $sectionType)->exists()) {
                $layout->sections()->create([
                    'type' => $sectionType,
                    'style' => [],
                    'layout' => $this->getDefaultGrid()
                ]);
            }
        }
    }

    protected function getDefaultHeaderGrid(): array
    {
        return [
            [
                [
                    'type' => 'image',
                    'field' => 'company.logo',
                    'colspan' => 1,
                    'rowspan' => 2,
                    'style' => ['class' => 'w-20 h-20'],
                ],
                [
                    'type' => 'text',
                    'field' => 'company.name',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-xl font-bold'],
                ]
            ],
            [
                [
                    'type' => 'text',
                    'field' => 'company.address',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-sm'],
                ]
            ]
        ];
    }

    protected function getDefaultBodyGrid(): array
    {
        return [
            [
                [
                    'type' => 'text',
                    'field' => 'invoice.title',
                    'colspan' => 2,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-lg font-bold text-center'],
                ]
            ],
            [
                [
                    'type' => 'text',
                    'field' => 'client.name',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'font-semibold'],
                ],
                [
                    'type' => 'text',
                    'field' => 'invoice.date',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-right'],
                ]
            ]
        ];
    }

    protected function getDefaultFooterGrid(): array
    {
        return [
            [
                [
                    'type' => 'text',
                    'field' => 'invoice.terms',
                    'colspan' => 2,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-sm text-gray-600'],
                ]
            ]
        ];
    }

    protected function getDefaultGrid(): array
    {
        return [
            [
                [
                    'type' => 'text',
                    'field' => null,
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => [],
                ],
                [
                    'type' => 'text',
                    'field' => null,
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => [],
                ]
            ]
        ];
    }
}
