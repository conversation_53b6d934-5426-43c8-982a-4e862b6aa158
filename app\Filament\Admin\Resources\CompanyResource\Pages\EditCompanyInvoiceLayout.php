<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Resources\Pages\Page;
use App\Models\Company;

class EditCompanyInvoiceLayout extends Page
{
    protected static string $resource = CompanyResource::class;

    protected static string $view = 'filament.admin.resources.company-resource.pages.edit-company-invoice-layout';

    public Company $company;

    public function mount($record): void
    {
        $this->company = Company::with('invoiceLayout.sections')->findOrFail($record);

        if (!$this->company->invoiceLayout) {
            $this->company->invoiceLayout()->create([
                'name' => 'Default Layout',
                'type' => 'invoice',
            ]);
            $this->company->refresh();
            $this->company->load('invoiceLayout.sections');
        }
    }
}
