<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Resources\Pages\Page;
use App\Models\Company;
use App\Models\InvoiceLayout;

class EditCompanyInvoiceLayout extends Page
{
    protected static string $resource = CompanyResource::class;
    protected static string $view = 'filament.admin.resources.company-resource.pages.edit-company-invoice-layout';

    public Company $record;
    public InvoiceLayout $invoiceLayout;

    public function mount(int|string $record): void
    {
        $this->record = Company::findOrFail($record);

        // Ambil atau buat layout
        $this->layout = $this->record->invoiceLayout()->firstOrCreate([]);
    }
}
