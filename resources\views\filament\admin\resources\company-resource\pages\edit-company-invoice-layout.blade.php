<x-filament::page>
    <div class="border p-4 rounded-xl bg-white shadow">
        <div class="flex justify-between items-center mb-2">
            <h3 class="text-lg font-semibold">Section: {{ $section->type }}</h3>
            <div class="space-x-2">
                <x-filament::button wire:click="addRow">+ Row</x-filament::button>
                <x-filament::button wire:click="addColumn">+ Column</x-filament::button>
                <x-filament::button wire:click="resetSection" color="danger">🔄 Reset</x-filament::button>
                <x-filament::button wire:click="deleteSection" color="danger">🗑️ Hapus</x-filament::button>
            </div>
        </div>

        <div class="w-full overflow-x-auto">
            <table class="table w-full border border-gray-300">
                <tbody>
                    @foreach ($grid as $rowIndex => $row)
                        <tr>
                            @foreach ($row as $colIndex => $cell)
                                <td class="border p-2 bg-white align-middle text-center relative group"
                                    colspan="{{ $cell['colspan'] ?? 1 }}"
                                    rowspan="{{ $cell['rowspan'] ?? 1 }}">

                                    <div class="text-xs text-gray-700">
                                        <strong>{{ $cell['type'] }}</strong><br>
                                        <small>{{ $cell['field'] ?? '–' }}</small>
                                    </div>

                                    {{-- Edit Button Hover --}}
                                    <button class="absolute top-1 right-1 bg-white border rounded p-1 text-xs shadow group-hover:opacity-100 opacity-0"
                                        x-data="{ open: false }" @click="open = !open">
                                        ⚙️
                                        <div x-show="open" class="absolute z-50 top-8 right-0 bg-white border rounded shadow p-2 w-48 text-left space-y-2">
                                            <div>
                                                <label class="text-xs font-medium">Type</label>
                                                <select wire:model="grid.{{ $rowIndex }}.{{ $colIndex }}.type"
                                                    class="w-full text-sm border-gray-300 rounded">
                                                    <option value="text">Text</option>
                                                    <option value="image">Image</option>
                                                </select>
                                            </div>

                                            <div>
                                                <label class="text-xs font-medium">Field</label>
                                                <select wire:model="grid.{{ $rowIndex }}.{{ $colIndex }}.field"
                                                    class="w-full text-sm border-gray-300 rounded">
                                                    <option value="">-- Select Field --</option>
                                                    @foreach ($availableFields as $key => $label)
                                                        <option value="{{ $key }}">{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </button>
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <x-filament::button wire:click="saveLayout" color="success" class="mt-4">
                💾 Simpan Layout
            </x-filament::button>
        </div>
    </div>
</x-filament::page>
