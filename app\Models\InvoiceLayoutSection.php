<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InvoiceLayoutSection extends Model
{
    protected $table = 'invoice_layout_sections';

    protected $fillable = [
        'invoice_layout_id',
        'type',
        'style',
		'layout',
    ];

	    protected $casts = [
			'style' => 'array',
			'layout' => 'array',
		];

	public function invoiceLayout(): BelongsTo
	{
		return $this->belongsTo(InvoiceLayout::class, 'invoice_layout_id');
	}


    public function cells(): HasMany
    {
        return $this->hasMany(InvoiceLayoutCell::class);
    }
}
