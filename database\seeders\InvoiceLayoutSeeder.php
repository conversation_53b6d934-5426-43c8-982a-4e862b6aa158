<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\InvoiceLayout;

class InvoiceLayoutSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all companies that don't have invoice layouts
        $companiesWithoutLayouts = Company::whereDoesntHave('invoiceLayout')->get();

        foreach ($companiesWithoutLayouts as $company) {
            $this->createDefaultLayoutForCompany($company);
        }
    }

    protected function createDefaultLayoutForCompany(Company $company)
    {
        // Create default layout
        $layout = $company->invoiceLayout()->create([
            'name' => 'Default Layout',
            'style' => []
        ]);

        // Create default sections
        $this->createDefaultSections($layout);
    }

    protected function createDefaultSections($layout)
    {
        $sections = [
            'header' => $this->getDefaultHeaderGrid(),
            'body' => $this->getDefaultBodyGrid(),
            'footer' => $this->getDefaultFooterGrid(),
        ];

        foreach ($sections as $type => $grid) {
            $layout->sections()->create([
                'type' => $type,
                'style' => [],
                'layout' => $grid
            ]);
        }
    }

    protected function getDefaultHeaderGrid(): array
    {
        return [
            [
                [
                    'type' => 'image',
                    'field' => 'company.logo',
                    'colspan' => 1,
                    'rowspan' => 2,
                    'style' => ['class' => 'w-20 h-20'],
                ],
                [
                    'type' => 'text',
                    'field' => 'company.name',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-xl font-bold'],
                ]
            ],
            [
                [
                    'type' => 'text',
                    'field' => 'company.address',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-sm'],
                ]
            ]
        ];
    }

    protected function getDefaultBodyGrid(): array
    {
        return [
            [
                [
                    'type' => 'text',
                    'field' => 'invoice.title',
                    'colspan' => 2,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-lg font-bold text-center'],
                ]
            ],
            [
                [
                    'type' => 'text',
                    'field' => 'client.name',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'font-semibold'],
                ],
                [
                    'type' => 'text',
                    'field' => 'invoice.date',
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-right'],
                ]
            ]
        ];
    }

    protected function getDefaultFooterGrid(): array
    {
        return [
            [
                [
                    'type' => 'text',
                    'field' => 'invoice.terms',
                    'colspan' => 2,
                    'rowspan' => 1,
                    'style' => ['class' => 'text-sm text-gray-600'],
                ]
            ]
        ];
    }
}
