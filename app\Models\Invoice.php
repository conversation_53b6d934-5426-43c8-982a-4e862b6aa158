<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'invoices';

    protected $fillable = [
        'order_id',
        'company_id',
        'client_id',
        'parent_invoice_id',
        'invoice_create_by',
        'invoice_no',
        'invoice_date',
        'due_date',
        'currency_id',
        'order_amount',
        'booking_fee',
        'invoice_amount',
        'status',
        'inv_sub_total',
        'rates',
        'amount_inword',
        'client_address',
        'bank_acc_name',
        'bank_code',
        'bank_acc_no',
        'bank_acc_address',
        'bank_name',
        'bank_address',
        'bank_correspondent',
        'swift',
        'swift_correspondent',
        'remarks',
        'routing_no',
        'transit',
        'tt_charge',
        'iban',
        'institution',
        'bsb',
        'branch_code',
        'sort_code',
        'branch_bank',
        'back2back',
        'ABA',
        'IFSC',
		'bank_custom_columns',
		'verification_status',
        'verified_by',
		'supervision_status',
        'supervised_by',
		'approval_status',
        'approved_by',
    ];

    protected $casts = [
        'bank_custom_columns' => 'array',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'invoice_create_by', 'id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function childInvoice()
    {
        return $this->hasMany(Invoice::class, 'parent_invoice_id', 'id');
    }

    public function parentInvoice()
    {
        return $this->belongsTo(Invoice::class, 'parent_invoice_id', 'id');
    }

    public function invoiceDetails()
    {
        return $this->hasMany(InvoiceDetail::class, 'invoice_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('order', function ($builder) {
            $builder->orderBy('created_at', 'desc');
        });
    }
    public function client()
    {
        return $this->belongsTo(Company::class, 'client_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    public function verifier()
    {
        return $this->belongsTo(User::class, 'verified_by', 'id');
    }

    public function supervisor()
    {
        return $this->belongsTo(User::class, 'supervised_by', 'id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by', 'id');
    }
}
