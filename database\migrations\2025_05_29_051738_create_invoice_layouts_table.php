<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_layouts', function (Blueprint $table) {
			$table->id();
			$table->unsignedBigInteger('company_id')->nullable();
			$table->string('name'); // nama layout (contoh: Default, Modern, dll)
			$table->json('style')->nullable(); // konfigurasi style global untuk layout
			$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_layouts');
    }
};
