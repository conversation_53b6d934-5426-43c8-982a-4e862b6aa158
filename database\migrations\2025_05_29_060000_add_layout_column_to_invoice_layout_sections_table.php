<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_layout_sections', function (Blueprint $table) {
            if (!Schema::hasColumn('invoice_layout_sections', 'layout')) {
                $table->json('layout')->nullable()->after('style');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_layout_sections', function (Blueprint $table) {
            if (Schema::hasColumn('invoice_layout_sections', 'layout')) {
                $table->dropColumn('layout');
            }
        });
    }
};
