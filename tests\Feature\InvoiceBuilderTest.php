<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Company;
use App\Models\InvoiceLayout;
use App\Models\InvoiceLayoutSection;
use Illuminate\Foundation\Testing\RefreshDatabase;

class InvoiceBuilderTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $company;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->company = Company::factory()->create();
    }

    /** @test */
    public function it_can_create_invoice_layout_for_company()
    {
        $layout = $this->company->invoiceLayout()->create([
            'name' => 'Test Layout',
            'style' => []
        ]);

        $this->assertInstanceOf(InvoiceLayout::class, $layout);
        $this->assertEquals('Test Layout', $layout->name);
        $this->assertEquals($this->company->id, $layout->company_id);
    }

    /** @test */
    public function it_can_create_sections_for_layout()
    {
        $layout = $this->company->invoiceLayout()->create([
            'name' => 'Test Layout',
            'style' => []
        ]);

        $section = $layout->sections()->create([
            'type' => 'header',
            'style' => [],
            'layout' => [
                [
                    [
                        'type' => 'text',
                        'field' => 'company.name',
                        'colspan' => 1,
                        'rowspan' => 1,
                        'style' => []
                    ]
                ]
            ]
        ]);

        $this->assertInstanceOf(InvoiceLayoutSection::class, $section);
        $this->assertEquals('header', $section->type);
        $this->assertIsArray($section->layout);
    }

    /** @test */
    public function it_can_access_invoice_layout_page()
    {
        $this->actingAs($this->user);

        $response = $this->get("/admin/companies/{$this->company->id}/invoice-layout");

        $response->assertStatus(200);
    }

    /** @test */
    public function it_auto_creates_layout_when_accessing_page()
    {
        $this->actingAs($this->user);

        // Ensure company doesn't have layout initially
        $this->assertNull($this->company->invoiceLayout);

        // Access the page
        $this->get("/admin/companies/{$this->company->id}/invoice-layout");

        // Refresh company and check if layout was created
        $this->company->refresh();
        $this->assertNotNull($this->company->invoiceLayout);
        $this->assertEquals('Default Layout', $this->company->invoiceLayout->name);
    }

    /** @test */
    public function it_creates_default_sections_automatically()
    {
        $this->actingAs($this->user);

        // Access the page to trigger auto-creation
        $this->get("/admin/companies/{$this->company->id}/invoice-layout");

        $this->company->refresh();
        $layout = $this->company->invoiceLayout;

        $this->assertNotNull($layout);
        
        // Check if all basic sections exist
        $sections = $layout->sections;
        $sectionTypes = $sections->pluck('type')->toArray();
        
        $this->assertContains('header', $sectionTypes);
        $this->assertContains('body', $sectionTypes);
        $this->assertContains('footer', $sectionTypes);
    }

    /** @test */
    public function section_has_valid_default_grid()
    {
        $this->actingAs($this->user);

        $this->get("/admin/companies/{$this->company->id}/invoice-layout");

        $this->company->refresh();
        $headerSection = $this->company->invoiceLayout->sections->where('type', 'header')->first();

        $this->assertNotNull($headerSection);
        $this->assertIsArray($headerSection->layout);
        $this->assertNotEmpty($headerSection->layout);
        
        // Check grid structure
        $grid = $headerSection->layout;
        $this->assertIsArray($grid[0]); // First row should be array
        $this->assertIsArray($grid[0][0]); // First cell should be array
        
        // Check cell structure
        $cell = $grid[0][0];
        $this->assertArrayHasKey('type', $cell);
        $this->assertArrayHasKey('field', $cell);
        $this->assertArrayHasKey('colspan', $cell);
        $this->assertArrayHasKey('rowspan', $cell);
        $this->assertArrayHasKey('style', $cell);
    }
}
