<div class="border p-4 rounded-xl bg-white shadow">
    {{-- Flash Messages --}}
    @if(session()->has('message'))
        <div class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ session('message') }}
        </div>
    @endif

    @if(session()->has('error'))
        <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {{ session('error') }}
        </div>
    @endif

    <div class="flex justify-between items-center mb-2">
        <h3 class="text-lg font-semibold">Section: {{ ucfirst($section->type) }}</h3>
        <div class="space-x-2">
            <x-filament::button wire:click="addRow" size="sm">+ Row</x-filament::button>
            <x-filament::button wire:click="addColumn" size="sm">+ Column</x-filament::button>
            <x-filament::button wire:click="resetLayout" color="danger" outlined size="sm">
                ♻️ Reset Layout
            </x-filament::button>
        </div>
    </div>

    <div class="w-full overflow-x-auto">
        <table class="table w-full border border-gray-300">
            <tbody>
                @foreach ($grid as $rowIndex => $row)
                    <tr>
                        @foreach ($row as $colIndex => $cell)
                            <td class="border p-2 bg-white align-middle text-center relative group"
                                colspan="{{ $cell['colspan'] ?? 1 }}"
                                rowspan="{{ $cell['rowspan'] ?? 1 }}">

                                <div class="text-xs text-gray-700">
                                    <strong>{{ $cell['type'] }}</strong><br>
                                    <small>{{ $cell['field'] ?? '–' }}</small>
                                </div>

                                {{-- Edit Button Hover --}}
                                <div x-data="{ open: false }" class="absolute top-1 right-1 group-hover:opacity-100 opacity-0">
                                    <button @click="open = !open"
                                        class="bg-white border rounded p-1 text-xs shadow">
                                        ⚙️
                                    </button>

                                    <div x-show="open"
                                         @click.outside="open = false"
                                         class="absolute z-50 top-8 right-0 bg-white border rounded shadow p-2 w-60 text-left space-y-2">

                                        <div>
                                            <label class="text-xs font-medium">Type</label>
                                            <select wire:model="grid.{{ $rowIndex }}.{{ $colIndex }}.type"
                                                class="w-full text-sm border-gray-300 rounded">
                                                <option value="text">Text</option>
                                                <option value="image">Image</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="text-xs font-medium">Field</label>
                                            <select wire:model="grid.{{ $rowIndex }}.{{ $colIndex }}.field"
                                                class="w-full text-sm border-gray-300 rounded">
                                                <option value="">-- Select Field --</option>
                                                @foreach ($availableFields as $key => $label)
                                                    <option value="{{ $key }}">{{ $label }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div>
                                            <label class="text-xs font-medium">Custom Style (class)</label>
                                            <input type="text"
                                                wire:model="grid.{{ $rowIndex }}.{{ $colIndex }}.style.class"
                                                class="w-full text-sm border-gray-300 rounded"
                                                placeholder="Contoh: text-xs text-gray-700">
                                        </div>

                                        <div class="text-right pt-1">
                                            <x-filament::button
                                                wire:click="removeCell({{ $rowIndex }}, {{ $colIndex }})"
                                                color="danger"
                                                size="sm">
                                                🗑 Hapus
                                            </x-filament::button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>

        <x-filament::button wire:click="saveLayout" color="success" class="mt-4">
            💾 Simpan Layout
        </x-filament::button>
    </div>
</div>
