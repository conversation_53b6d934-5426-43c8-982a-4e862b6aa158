<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_layout_cells', function (Blueprint $table) {
			$table->id();
			$table->foreignId('layout_section_id')->constrained()->onDelete('cascade'); // relasi ke section
			$table->integer('row'); // posisi baris pada grid
			$table->integer('col'); // posisi kolom pada grid
			$table->integer('rowspan')->default(1); // jumlah baris yang digabung
			$table->integer('colspan')->default(1); // jumlah kolom yang digabung
			$table->enum('type', ['text', 'image', 'field'])->default('text'); // jenis isi: teks, gambar, field (dinamis)
			$table->text('content')->nullable(); // isi konten: teks langsung, path gambar, atau nama field (mis. 'company.name')
			$table->json('style')->nullable(); // styling khusus cell
			$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_layout_cells');
    }
};
