<?php

namespace App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Filament\Admin\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
			//Action back to listrecord (filament.admin.resources.invoices.index atau route('filament.admin.resources.invoices.index'))
            Actions\Action::make('back')
                ->label('Back')
				->icon('heroicon-o-arrow-left')
				->color('gray')
                ->url(route('filament.admin.resources.invoices.index')),
            Actions\EditAction::make(),
        ];
    }

    public function getSubheading(): string|Htmlable
    {
        return new HtmlString(view('components.invoice-quick-navigation', [
            'invoice' => $this->record,
            'mode' => 'view'
        ])->render());
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Transaction Chain Overview - Moved to top for context
                Section::make('Transaction Chain Overview')
                    ->schema([
                        ViewEntry::make('chain_visualization')
                            ->hiddenLabel()
                            ->view('components.invoice-chain-visualization', [
                                'invoice' => $this->record
                            ])
                    ])
                    ->collapsible()
                    ->collapsed(false),

                // Basic Invoice Information
                Section::make('Invoice Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('invoice_no')
                                    ->label('Invoice Number')
                                    ->weight('bold')
                                    ->color('primary'),
                                TextEntry::make('invoice_date')
                                    ->label('Invoice Date')
                                    ->date(),
                                TextEntry::make('due_date')
                                    ->label('Due Date')
                                    ->date(),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn ($record) => match ($record->status) {
                                        'Draft' => 'gray',
                                        'Issued' => 'warning',
                                        'Closed' => 'success',
                                        default => 'gray'
                                    }),
                                TextEntry::make('currency.code')
                                    ->label('Currency')
                                    ->badge()
                                    ->color('info'),
                            ]),
                    ])
                    ->columns(1),

                // Company & Client Information
                Section::make('Company & Client Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('company.name')
                                    ->label('Company (Issuer)')
                                    ->weight('bold')
                                    ->color('primary'),
                                TextEntry::make('client.name')
                                    ->label('Client (Recipient)')
                                    ->weight('bold')
                                    ->color('success'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('company.address')
                                    ->label('Company Address')
                                    ->html()
                                    ->placeholder('No address provided'),
                                TextEntry::make('client_address')
                                    ->label('Client Address')
                                    ->html()
                                    ->placeholder('No address provided'),
                            ]),
                    ])
                    ->columns(1),

                // Financial Information
                Section::make('Financial Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('order_amount')
                                    ->label('Order Amount')
                                    ->money(fn ($record) => $record->currency?->code ?? 'USD')
                                    ->color('info'),
                                TextEntry::make('booking_fee')
                                    ->label('Booking Fee')
                                    ->money(fn ($record) => $record->currency?->code ?? 'USD')
                                    ->placeholder('0.00'),
                                TextEntry::make('invoice_amount')
                                    ->label('Invoice Amount')
                                    ->money(fn ($record) => $record->currency?->code ?? 'USD')
                                    ->weight('bold')
                                    ->color('primary'),
                            ]),
                        TextEntry::make('amount_inword')
                            ->label('Amount in Words')
                            ->placeholder('Not specified')
                            ->columnSpanFull(),
                        // Show rates only for child invoices
                        TextEntry::make('rates')
                            ->label('Exchange Rate')
                            ->prefix('Rp ')
                            ->numeric()
                            ->visible(fn ($record) => $record->parent_invoice_id !== null),
                    ])
                    ->columns(1),

                // Invoice Details
                Section::make('Invoice Details')
                    ->schema([
                        ViewEntry::make('invoice_details_table')
                            ->hiddenLabel()
                            ->view('components.invoice-details-table', [
                                'invoice' => $this->record
                            ])
                            ->columnSpanFull()
                    ])
                    ->collapsible()
                    ->collapsed(false),

                // Bank Information (if available)
                Section::make('Bank Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('bank_acc_name')
                                    ->label('Account Name')
                                    ->placeholder('Not specified'),
                                TextEntry::make('bank_name')
                                    ->label('Bank Name')
                                    ->placeholder('Not specified'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('bank_acc_no')
                                    ->label('Account Number')
                                    ->placeholder('Not specified'),
                                TextEntry::make('swift')
                                    ->label('SWIFT Code')
                                    ->placeholder('Not specified'),
                            ]),
                        TextEntry::make('bank_address')
                            ->label('Bank Address')
                            ->placeholder('Not specified')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(true)
                    ->visible(fn ($record) => $record->bank_name || $record->bank_acc_no || $record->swift),

                // Chain Information
                Section::make('Chain Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('parent_invoice_id')
                                    ->label('Parent Invoice')
                                    ->formatStateUsing(fn ($state) => $state ? "Invoice #{$state}" : 'Main Invoice')
                                    ->color(fn ($record) => $record->parent_invoice_id ? 'info' : 'success')
                                    ->badge(),
                                TextEntry::make('creator.name')
                                    ->label('Created By')
                                    ->placeholder('Unknown'),
                            ]),
                        TextEntry::make('remarks')
                            ->label('Remarks')
                            ->placeholder('No remarks')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(true),
            ]);
    }
}
