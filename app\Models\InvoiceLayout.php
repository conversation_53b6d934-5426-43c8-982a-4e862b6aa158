<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InvoiceLayout extends Model
{
	use HasFactory;

	protected $table = 'invoice_layouts';

    protected $fillable = [
		'company_id',
        'name',
        'style',
    ];

	protected $casts = [
        'style' => 'array',
    ];

    public function sections(): HasMany
    {
        return $this->hasMany(InvoiceLayoutSection::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
