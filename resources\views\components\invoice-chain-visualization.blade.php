@props(['invoice'])

@php
    // Build chain data
    $chainData = [];
    $current = $invoice;

    // Go to root (main invoice)
    while ($current->parent_invoice_id) {
        $current = $current->parentInvoice;
    }

    // Build chain from root
    $chainData[] = $current;
    while ($current->childInvoice()->exists()) {
        $current = $current->childInvoice()->first();
        $chainData[] = $current;
    }

    $currentInvoiceId = $invoice->id;
    $totalChain = count($chainData);
@endphp

<div class="bg-gray-50 p-4 rounded-lg border">
    <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-medium text-gray-900"> </h4>
        <span class="text-xs text-gray-500">{{ $totalChain }} {{ $totalChain > 1 ? 'companies' : 'company' }} in chain</span>
    </div>

    <div class="flex items-center space-x-2 overflow-x-auto pb-2">
        @foreach($chainData as $index => $chainInvoice)
            {{-- Company Box --}}
            <div class="flex-shrink-0 relative w-40">
                <div class="flex flex-col items-center w-full">
                    {{-- Company Badge --}}
                    <div class="px-4 py-3 rounded-lg border-2 text-center w-40 h-32 flex flex-col justify-between relative"
                         style="{{ $chainInvoice->id === $currentInvoiceId
                            ? 'background-color: #eff6ff; border-color: #2563eb; color: #1e3a8a; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);'
                            : 'background-color: white; border-color: #d1d5db; color: #374151;' }}"
                         onmouseover="{{ $chainInvoice->id !== $currentInvoiceId ? 'this.style.borderColor=\'#9ca3af\'' : '' }}"
                         onmouseout="{{ $chainInvoice->id !== $currentInvoiceId ? 'this.style.borderColor=\'#d1d5db\'' : '' }}">

                        {{-- Header --}}
                        <div class="text-xs font-medium text-gray-600">
                            {{ $index === 0 ? 'Main Invoice' : 'Chain Level ' . ($index + 1) }}
                        </div>

                        {{-- Company Name --}}
                        <div class="flex-1 flex items-center justify-center">
                            <div class="text-sm font-semibold leading-tight text-center overflow-hidden"
                                 title="{{ $chainInvoice->company->name ?? 'Unknown' }}">
                                <div style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; word-break: break-word; line-height: 1.2;">
                                    {{ $chainInvoice->company->name ?? 'Unknown' }}
                                </div>
                            </div>
                        </div>

                        {{-- Footer --}}
                        <div class="space-y-1">
                            <div class="text-xs text-gray-500">
                                #{{ $chainInvoice->id }}
                            </div>
                            <div class="text-xs">
                                <span class="px-1.5 py-0.5 rounded text-xs font-medium"
                                      style="{{ $chainInvoice->status === 'Draft' ? 'background-color: #f3f4f6; color: #6b7280;' : '' }}
                                             {{ $chainInvoice->status === 'Issued' ? 'background-color: #fef3c7; color: #d97706;' : '' }}
                                             {{ $chainInvoice->status === 'Closed' ? 'background-color: #d1fae5; color: #059669;' : '' }}">
                                    {{ $chainInvoice->status }}
                                </span>
                            </div>
                        </div>
                    </div>

                    {{-- Client Info --}}
                    @if($chainInvoice->client)
                        <div class="mt-2 text-xs text-gray-500 text-center w-40 h-4 flex items-center justify-center"
                             title="{{ $chainInvoice->client->name }}">
                            <div class="truncate px-2" style="max-width: 100%;">
                                @php
                                    $clientName = $chainInvoice->client->name;
                                    $maxLength = 25; // Limit to 25 characters
                                    if (strlen($clientName) > $maxLength) {
                                        $clientName = substr($clientName, 0, $maxLength - 3) . '...';
                                    }
                                @endphp
                                → {{ $clientName }}
                            </div>
                        </div>
                    @endif
                </div>


            </div>

            {{-- Arrow --}}
            @if($index < count($chainData) - 1)
                <div class="flex-shrink-0 text-gray-400 flex items-center px-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </div>
            @endif
        @endforeach
    </div>

    {{-- Chain Summary --}}
    <div class="mt-3 pt-3 border-t border-gray-200">
        <div class="flex justify-between text-xs text-gray-600">
            <span>
                <strong>Your Position:</strong>
                @php
                    $currentIndex = array_search($currentInvoiceId, array_column($chainData, 'id'));
                @endphp
                {{ $currentIndex === 0 ? 'Main Invoice' : 'Chain Level ' . ($currentIndex + 1) }}
                ({{ $currentIndex + 1 }} of {{ $totalChain }})
            </span>
            <span>
                <strong>Total Value:</strong>
                {{ $chainData[0]->currency->symbol ?? '$' }} {{ number_format($chainData[0]->invoice_amount ?? 0, 2) }}
            </span>
        </div>
    </div>

    {{-- Quick Actions --}}
    <div class="mt-3 pt-3 border-t border-gray-200">
        <div class="flex space-x-2">
            @if($invoice->parent_invoice_id)
                <a href="{{ route('filament.admin.resources.invoices.view', $invoice->parent_invoice_id) }}"
                   class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    View Parent
                </a>
            @endif

            @if($invoice->childInvoice()->exists())
                <a href="{{ route('filament.admin.resources.invoices.view', $invoice->childInvoice()->first()->id) }}"
                   class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                    View Child
                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m-7-7h18"></path>
                    </svg>
                </a>
            @endif

            @if($totalChain === 1)
                <span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Single Invoice (No Chain)
                </span>
            @endif
        </div>
    </div>
</div>
