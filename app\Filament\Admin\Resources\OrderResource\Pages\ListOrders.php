<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\OrderResource;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\Invoice;
use App\Models\MasterInvoiceDetail;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Select};
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\{SelectColumn, TextColumn, TextInputColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('New Order')
                ->modalHeading('New Order')
                ->icon('heroicon-o-plus')
                ->modalWidth('xl'),
        ];
    }

    protected static ?string $title = 'Orders';

    public function getHeading(): string
	{
        if(!Auth::user()->hasRole('Staff Order'))
        {
            return 'Orders tobe Invoiced';
        }
        return 'Orders';
	}

    protected function paginateTableQuery(Builder $query): Paginator
    {
        return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                if (Auth::user()->hasAnyRole(['Staff Invoice'])) {
                    return $query->whereIn('status', ['Forwarded', 'Invoiced']);
                }
            })
            ->columns([
				SelectColumn::make('company_id')
					->label('Company')
					->options(Company::where('type', 2)->pluck('name', 'id'))
					->searchable()
					->sortable(),
                TextInputColumn::make('order_date')
                    ->type('date')
                    ->sortable(),
                TextColumn::make('order_amount')
                    ->formatStateUsing(fn ($record, $state) => $record->currency->symbol . ' ' . number_format($state, 2, ',', '.'))
                    ->alignEnd()
                    ->sortable(),
                TextColumn::make('booking_fee')
                    ->formatStateUsing(fn ($record, $state) => $record->currency->symbol . ' ' . number_format($state, 2, ',', '.'))
                    ->alignEnd()
                    ->sortable(),
                TextColumn::make('total')
                    ->formatStateUsing(fn ($record, $state) => $record->currency->symbol . ' ' . number_format($state, 2, ',', '.'))
                    ->alignEnd()
                    ->sortable(),
                TextColumn::make('status')
                    ->searchable()
					->badge()
					->color(fn ($record) => match ($record->status) {
						'Draft' => 'danger',
						'Forwarded' => 'warning',
						'Invoiced' => 'success',
					})
					->formatStateUsing(function ($state) {
						if(Auth::user()->hasRole('Staff Invoice'))
						{
							return match ($state) {
								'Forwarded' => 'New',
								'Invoiced' => 'Invoiced',
							};
						}
						return match ($state) {
							'Draft' => 'Draft',
							'Forwarded' => 'New',
							'Invoiced' => 'Invoiced',
						};
					}),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('created_from')
                    ->form([
                        DatePicker::make('date_from')->label('Created From'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['date_from'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                        );
                    }),
                Filter::make('created_until')
                    ->form([
                        DatePicker::make('date_until')->label('Created Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['date_until'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                        );
                    }),
                SelectFilter::make('status')
                    ->options(function () {
                        if(Auth::user()->hasRole('Staff Invoice'))
                        {
                            return [
                                'Forwarded' => 'New',
                                'Invoiced' => 'Invoiced',
                            ];
                        }
                        if(Auth::user()->hasRole('Staff Order'))
                        {
                            return [
                                'Draft' => 'Draft',
                                'Forwarded' => 'Forwarded',
                            ];
                        }
                        return [];
                    })
                    ->default(function () {
                        if(Auth::user()->hasRole('Staff Invoice'))
                        {
                            return 'Forwarded';
                        }
                        if(Auth::user()->hasRole('Staff Order'))
                        {
                            return 'Draft';
                        }
                        return '';
                    }),
            ], layout: FiltersLayout::AboveContentCollapsible)
            ->filtersFormColumns(3)
			->hiddenFilterIndicators()
            ->actions([
                ViewAction::make()
                    ->iconButton()
                    ->tooltip('View')
                    ->modalWidth('xl')
                    ->modalHeading(fn ($record) => 'Order '.' '. $record->company->name),
                EditAction::make()
                    ->iconButton()
                    ->tooltip('Edit')
                    ->visible(fn () => Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']))
                    ->hidden(fn (Order $record) => $record->status !== 'Draft'),
                Action::make('forward')
                    ->label('Forward')
                    ->icon('heroicon-o-forward')
                    ->iconButton()
                    ->tooltip('Send to Invoicing')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Forward Order to Invoicing')
                    ->modalDescription('You are about to forward this order to the Invoicing Team. Are you sure you want to continue?.')
                    ->modalSubmitActionLabel('Yes, Proceed')
                    ->visible(fn () => Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']))
                    ->hidden(fn (Order $record) => $record->status !== 'Draft')
                    ->action(function (Order $record) {
                        try {
                            $record->update(['status' => 'Forwarded']);

                            Notification::make()
                                ->title('Berhasil')
                                ->body('Order forwarded to the Invoicing Team.')
                                ->success()
                                ->send();
                        } catch (\Throwable $e) {
                            Notification::make()
                                ->title('Gagal')
                                ->body('Terjadi kesalahan saat mencoba mem-forward order.')
                                ->danger()
                                ->send();
                        }
                    }),

                Action::make('invoice')
                    ->label('Invoice')
                    ->icon('heroicon-o-clipboard-document-check')
                    ->iconButton()
                    ->tooltip('Create Invoice')
                    ->color('primary')
                    ->requiresConfirmation()
                    ->modalHeading('Create Invoice')
                    ->modalDescription('You are about to create an invoice for this order. Are you sure you want to continue?.')
                    ->modalSubmitActionLabel('Yes, Proceed')
                    ->form([
                        Select::make('client_id')
                            ->label('Select Company')
                            ->helperText('Note: This will become the company issuing the invoice')
                            ->searchable()
                            ->options(function ($record) {
                                return Company::whereNotIn('id', [
                                    $record->company_id, // Hindari client asal
                                ])->where('type', 1)->pluck('name', 'id');
                            }) // sesuaikan model & kolom
                            ->required(),
                    ])
                    ->visible(fn () => Auth::user()->hasAnyRole(['Staff Invoice', 'Admin', 'Super Admin']))
                    ->hidden(fn (Order $record) => $record->status !== 'Forwarded')
                    ->action(function (Order $record, array $data) {
                        $invoice = DB::transaction(function () use ($record, $data) {
                            $record->update(['status' => 'Invoiced']);
							// Perbaikan: company_id di order seharusnya adalah client_id di invoice
							// dan client_id yang dipilih seharusnya adalah company_id di invoice
							$client = Company::find($record->company_id); // Client dari order
							$company = Company::find($data['client_id']); // Company yang dipilih

							// Detail invoice diambil berdasarkan business type company yang mengeluarkan invoice
							// Sama seperti bank information, ambil semua karena tidak ada default
							$detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();
							// Bank diambil dari company yang dipilih (bukan dari order)
							$bank = CompanyBank::where('company_id', $data['client_id'])->where('is_default', true)->first();
							if(!$bank)
							{
								$bank = CompanyBank::where('company_id', $data['client_id'])->first();
							}
                            // Buat invoice terlebih dahulu
                            $invoice = \App\Models\Invoice::create([
                                'order_id' => $record->id,
                                'company_id' => $data['client_id'], // Company yang dipilih
                                'client_id' => $record->company_id, // Client dari order
                                'booking_fee' => $record->booking_fee,
								'rates' => $record->rates,
                                'order_amount' => $record->order_amount,
                                'invoice_amount' => $record->total,
                                'status' => 'Draft',
								'invoice_date'=> $record->order_date,
								'due_date' => Carbon::parse($record->order_date)->addDays(7),
								'invoice_create_by' => Auth::user()->id,
								'currency_id' => $record->currency_id,
								//include client address (sesuai client address)
								'client_address' => $client->address,

								//dan bank information (sesuai company bank)
								'bank_acc_name' => $bank->bank_acc_name,
								'bank_code' => $bank->bank_code,
								'bank_acc_no' => $bank->bank_acc_no,
								'bank_acc_address' => $bank->bank_acc_address,
								'bank_name' => $bank->bank_name,
								'bank_address' => $bank->bank_address,
								'bank_correspondent' => $bank->bank_correspondent,
								'swift' => $bank->swift,
								'swift_correspondent' => $bank->swift_correspondent,
								'routing_no' => $bank->routing_no,
								'transit' => $bank->transit,
								'tt_charge' => $bank->tt_charge,
								'iban' => $bank->iban,
								'institution' => $bank->institution,
								'bsb' => $bank->bsb,
								'branch_code' => $bank->branch_code,
								'sort_code' => $bank->sort_code,
								'branch_bank' => $bank->branch_bank,
								'back2back' => $bank->back2back,
								'ABA' => $bank->ABA,
								'IFSC' => $bank->IFSC,
								'bank_custom_columns' => $bank->custom_columns,
                            ]);

                            // Tambahkan detail invoice dari master invoice detail sesuai business type company yang mengeluarkan invoice
                            if ($detail->isNotEmpty()) {
                                foreach ($detail as $item) {
                                    $invoice->invoiceDetails()->create([
                                        'company_id' => $data['client_id'], // Company yang mengeluarkan invoice
                                        'client_id' => $record->company_id, // Client penerima invoice (dari order)
                                        'description' => $item->description,
                                        'quantity' => $item->quantity,
                                        'unit' => $item->unit,
                                        'price' => $item->price,
                                        'sub_total' => $item->quantity * $item->price,
                                        'status' => 'Active',
                                    ]);
                                }

                                // Hitung ulang total invoice
                                $total = $invoice->invoiceDetails()->sum('sub_total');
                                $invoice->invoice_amount = $total;
                                $invoice->inv_sub_total = $total;
                                $invoice->save();
                            }

                            return $invoice;

                        });
                        Notification::make()
                            ->title('Invoice created successfully. Please edit the invoice to complete the process.')
                            ->success()
                            ->send();
                        // 3. Redirect ke halaman edit invoice
                        return redirect()->route('filament.admin.resources.invoices.edit', ['record' => $invoice->id]);
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
