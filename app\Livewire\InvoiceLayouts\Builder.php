<?php

namespace App\Livewire\InvoiceLayouts;

use App\Models\InvoiceLayout;
use Livewire\Component;

class Builder extends Component
{
	public InvoiceLayout $layout;

    public function mount(InvoiceLayout $layout)
    {
        $this->layout = $layout->load('sections.cells');

        // Ensure basic sections exist
        $this->ensureBasicSections();
    }

    public function addSection()
    {
        $this->layout->sections()->create([
            'type' => 'body',
            'style' => [],
            'layout' => $this->getDefaultGrid()
        ]);

        $this->layout->refresh();
        $this->layout->load('sections.cells');

        session()->flash('message', 'Section baru berhasil ditambahkan!');
    }

    public function saveLayout()
    {
        // Save any pending changes
        session()->flash('message', 'Layout berhasil disimpan!');
    }

    protected function ensureBasicSections()
    {
        $basicSections = ['header', 'body', 'footer'];

        foreach ($basicSections as $sectionType) {
            if (!$this->layout->sections()->where('type', $sectionType)->exists()) {
                $this->layout->sections()->create([
                    'type' => $sectionType,
                    'style' => [],
                    'layout' => $this->getDefaultGrid()
                ]);
            }
        }

        $this->layout->refresh();
        $this->layout->load('sections.cells');
    }

    protected function getDefaultGrid(): array
    {
        return [
            [
                [
                    'type' => 'text',
                    'field' => null,
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => [],
                ],
                [
                    'type' => 'text',
                    'field' => null,
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => [],
                ]
            ]
        ];
    }

    public function render()
    {
        return view('livewire.invoice-layouts.builder');
    }
}
