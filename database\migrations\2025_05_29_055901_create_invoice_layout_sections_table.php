<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_layout_sections', function (Blueprint $table) {
			$table->id();
			$table->foreignId('invoice_layout_id')->constrained()->onDelete('cascade'); // relasi ke layout
			$table->string('type'); // jenis section: header, body, footer
			$table->json('style')->nullable(); // styling khusus section
			$table->json('layout')->nullable(); // grid layout data
			$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_layout_sections');
    }
};
