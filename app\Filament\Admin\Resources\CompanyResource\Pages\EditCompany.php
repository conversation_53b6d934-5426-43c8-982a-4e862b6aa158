<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditCompany extends EditRecord
{
    protected static string $resource = CompanyResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
			Action::make('editLayout')
				->label('Edit Layout Invoice')
				->url(fn () => route('filament.admin.resources.companies.invoiceLayout', $this->record))
				->icon('heroicon-m-document-text'),
        ];
    }
}
