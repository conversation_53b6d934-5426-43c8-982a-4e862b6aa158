<?php

namespace App\Livewire\InvoiceLayouts;

use App\Models\InvoiceLayoutSection;
use Livewire\Component;

class SectionEditor extends Component
{
	public InvoiceLayoutSection $section;

    public array $grid = []; // 2D array: rows -> cells
	public array $availableFields = [];

    public function mount()
    {
        // Load grid dari section layout (jika ada), atau inisialisasi default
        $this->grid = $this->section->layout
            ? json_decode($this->section->layout, true)
            : $this->initializeGrid(2, 2); // default 2x2

		$this->availableFields = $this->getAvailableFields($this->section->type);
    }

	protected function getAvailableFields(string $type): array
	{
		return match ($type) {
			'header' => [
				'company.name' => 'Company Name',
				'company.address' => 'Company Address',
				'company.phone' => 'Phone',
				'company.email' => 'Email',
				'company.logo' => 'Company Logo',
				'company.signature' => 'Signature',
			],
			default => [],
		};
	}

    public function initializeGrid(int $rows, int $cols): array
    {
        $grid = [];
        for ($i = 0; $i < $rows; $i++) {
            $row = [];
            for ($j = 0; $j < $cols; $j++) {
                $row[] = [
                    'type' => 'text',     // text / image
                    'field' => null,      // e.g., 'company.name'
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => [],        // styling opsional
                ];
            }
            $grid[] = $row;
        }
        return $grid;
    }

    public function addRow()
    {
        $cols = count($this->grid[0] ?? []);
        $this->grid[] = $this->initializeGrid(1, $cols)[0];
    }

    public function addColumn()
    {
        foreach ($this->grid as &$row) {
            $row[] = [
                'type' => 'text',
                'field' => null,
                'colspan' => 1,
                'rowspan' => 1,
                'style' => [],
            ];
        }
    }

	public function saveLayout()
	{
		// $this->section->layout = json_encode($this->grid);
		$this->section->layout = $this->grid;
		$this->section->save();

		session()->flash('message', 'Layout berhasil disimpan!');
	}

	public function removeCell($rowIndex, $colIndex)
	{
		unset($this->grid[$rowIndex][$colIndex]);
		$this->grid[$rowIndex] = array_values($this->grid[$rowIndex]); // Reindex kolom
	}

	public function resetLayout()
	{
		$this->grid = $this->initializeGrid(2, 2); // Reset ke 2x2
	}


    public function render()
    {
        return view('livewire.invoice-layouts.section-editor');
    }
}
