<?php

namespace App\Livewire\InvoiceLayouts;

use App\Models\InvoiceLayoutSection;
use Livewire\Component;

class SectionEditor extends Component
{
	public InvoiceLayoutSection $section;

    public array $grid = []; // 2D array: rows -> cells
	public array $availableFields = [];

    public function mount()
    {
        // Load grid dari section layout (jika ada), atau inisialisasi default
        $this->grid = $this->section->layout
            ? (is_array($this->section->layout) ? $this->section->layout : json_decode($this->section->layout, true))
            : $this->initializeGrid(2, 2); // default 2x2

		$this->availableFields = $this->getAvailableFields($this->section->type);
    }

	protected function getAvailableFields(string $type): array
	{
		return match ($type) {
			'header' => [
				'company.name' => 'Company Name',
				'company.address' => 'Company Address',
				'company.phone' => 'Phone',
				'company.email' => 'Email',
				'company.fax' => 'Fax',
				'company.website' => 'Website',
				'company.logo' => 'Company Logo',
				'company.signature' => 'Signature',
				'invoice.title' => 'Invoice Title',
				'invoice.date' => 'Invoice Date',
				'invoice.number' => 'Invoice Number',
			],
			'body' => [
				'client.name' => 'Client Name',
				'client.address' => 'Client Address',
				'client.phone' => 'Client Phone',
				'client.email' => 'Client Email',
				'invoice.date' => 'Invoice Date',
				'invoice.due_date' => 'Due Date',
				'invoice.number' => 'Invoice Number',
				'invoice.amount' => 'Invoice Amount',
				'invoice.currency' => 'Currency',
				'invoice.details' => 'Invoice Details Table',
				'invoice.subtotal' => 'Subtotal',
				'invoice.total' => 'Total Amount',
				'invoice.amount_in_words' => 'Amount in Words',
				'bank.name' => 'Bank Name',
				'bank.account_name' => 'Account Name',
				'bank.account_number' => 'Account Number',
				'bank.swift' => 'SWIFT Code',
				'bank.address' => 'Bank Address',
			],
			'footer' => [
				'company.name' => 'Company Name',
				'company.signature' => 'Signature',
				'invoice.terms' => 'Terms & Conditions',
				'invoice.notes' => 'Notes',
				'invoice.footer_text' => 'Footer Text',
				'current.date' => 'Current Date',
			],
			default => [
				'custom.text' => 'Custom Text',
				'custom.image' => 'Custom Image',
			],
		};
	}

    public function initializeGrid(int $rows, int $cols): array
    {
        $grid = [];
        for ($i = 0; $i < $rows; $i++) {
            $row = [];
            for ($j = 0; $j < $cols; $j++) {
                $row[] = [
                    'type' => 'text',     // text / image
                    'field' => null,      // e.g., 'company.name'
                    'colspan' => 1,
                    'rowspan' => 1,
                    'style' => [],        // styling opsional
                ];
            }
            $grid[] = $row;
        }
        return $grid;
    }

    public function addRow()
    {
        $cols = count($this->grid[0] ?? []);
        $this->grid[] = $this->initializeGrid(1, $cols)[0];
    }

    public function addColumn()
    {
        foreach ($this->grid as &$row) {
            $row[] = [
                'type' => 'text',
                'field' => null,
                'colspan' => 1,
                'rowspan' => 1,
                'style' => [],
            ];
        }
    }

	public function saveLayout()
	{
		try {
			// Validate grid structure
			if (empty($this->grid)) {
				session()->flash('error', 'Grid tidak boleh kosong!');
				return;
			}

			// Save layout
			$this->section->layout = $this->grid;
			$this->section->save();

			session()->flash('message', 'Layout section "' . ucfirst($this->section->type) . '" berhasil disimpan!');
		} catch (\Exception $e) {
			session()->flash('error', 'Gagal menyimpan layout: ' . $e->getMessage());
		}
	}

	public function removeCell($rowIndex, $colIndex)
	{
		// Remove the cell
		unset($this->grid[$rowIndex][$colIndex]);

		// Reindex columns
		$this->grid[$rowIndex] = array_values($this->grid[$rowIndex]);

		// If row becomes empty, remove the entire row
		if (empty($this->grid[$rowIndex])) {
			unset($this->grid[$rowIndex]);
			$this->grid = array_values($this->grid); // Reindex rows
		}

		// If grid becomes completely empty, initialize with default
		if (empty($this->grid)) {
			$this->grid = $this->initializeGrid(1, 1);
		}
	}

	public function resetLayout()
	{
		$this->grid = $this->initializeGrid(2, 2); // Reset ke 2x2
	}


    public function render()
    {
        return view('livewire.invoice-layouts.section-editor');
    }
}
