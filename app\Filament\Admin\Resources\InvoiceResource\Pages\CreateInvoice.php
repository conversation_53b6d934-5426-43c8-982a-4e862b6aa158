<?php

namespace App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Filament\Admin\Resources\InvoiceResource;
use App\Models\Currency;
use App\Models\Invoice;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Group, Hidden, Placeholder, RichEditor, Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'New Invoice';

    public function getHeading(): string
	{
        return 'New Invoice';
	}

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-md"></span>');
    }

    // public function form(Form $form): Form
    // {
    //     return $form
    //         ->columns(12)
    //         ->schema([
	// 			Hidden::make('order_id'),
	// 			Hidden::make('invoice_create_by')
	// 				->default(Auth::user()->id),
	// 			Hidden::make('parent_invoice_id'),
	// 			Hidden::make('status'),
	// 			Select::make('company_id')
	// 				->label('Select Company')
	// 				->options(function ($record) {
	// 					return \App\Models\Company::pluck('name', 'id');
	// 				})
	// 				->searchable()
	// 				->reactive()
	// 				->label('Company')
	// 				->inlineLabel()
	// 				->columnSpanFull()
	// 				->required()
	// 				->afterStateUpdated(function (callable $set, $state, callable $get) {
	// 					// static::templateSelect($set, $state);
	// 					// static::calculateFields($get, $set);
	// 					static::updateInvoiceNumberPlaceholder($set, $state);
	// 				}),
    //             Section::make('Basic Information')
	// 				->columnSpan(6)
    //                 ->schema([
    //                     TextInput::make('invoice_number')
	// 						->placeholder(fn (callable $get) => $get('invoice_no_placeholder'))
	// 						->live(onBlur:true)
	// 						->inlineLabel()
	// 						->required()
	// 						->hintColor('danger')
    //                         ->maxLength(191)->hint(fn (callable $get) => $get('duplicate_warning'))
	// 						->afterStateUpdated(function (callable $set, $state, callable $get, $record) {

	// 							if(!$record || $record->replicate_no === 'Main'){
	// 								$set('main_invoice', $state);
	// 							}else{
	// 								$set('main_invoice', $get('main_invoice'));
	// 							}
	// 							$isDuplicate = Invoice::where('invoice_no', $state)->first();
	// 							$set('duplicate_warning', $isDuplicate ? 'Duplicate Invoice Number' : '');
	// 							if($isDuplicate){
	// 								$set('invoice_no', null);

	// 								Notification::make()
	// 									->title('Error')
	// 									->danger()
	// 									->body('Invoice has been used. Get another invoice number.')
	// 									->send();
	// 							}
	// 						}),
	// 					Select::make('currency_id')
    //                         ->label('Currency')
	// 						->inlineLabel()
    //                         ->options(
    //                             \App\Models\Currency::get()
    //                                 ->mapWithKeys(fn ($currency) => [
    //                                     $currency->id => "{$currency->symbol} - {$currency->name}"
    //                                 ])
    //                                 ->toArray()
    //                         )
    //                         ->searchable()
    //                         ->required()
	// 							->afterStateUpdated(function ($state, callable $set) {
	// 								$currency = Currency::find($state);
	// 								if ($currency) {
	// 									$set('currency_symbol', $currency->symbol);
	// 								}
	// 							})
	// 							->afterStateHydrated(function ($state, callable $set) {
	// 								$currency = Currency::find($state);
	// 								if ($currency) {
	// 									$set('currency_symbol', $currency->symbol);
	// 								}
	// 							})
	// 							->reactive(),
    //                     DatePicker::make('invoice_date')
	// 						->reactive()
	// 						->inlineLabel()
	// 						->default(today())
	// 						->afterStateUpdated(function (callable $set, $state) {
	// 							$maxDue = Carbon::parse($state)->addDays(14)->toDateString();
	// 							$set('due_date', $maxDue);
	// 						}),
    //                     DatePicker::make('due_date')
	// 						->minDate(fn($get)=>$get('invoice_date'))
	// 						->inlineLabel()
	// 						->default(fn ($get) => $get('invoice_date')
	// 							? Carbon::parse($get('invoice_date'))->addDays(14)->toDateString()
	// 							: today()->addDays(14)->toDateString())
	// 						->minDate(fn($get)=>$get('invoice_date')),
    //                 ]),

	// 			Section::make('Client Information')
	// 				->columnSpan(6)
	// 				->schema([
	// 					Select::make('client_id')
	// 						->label('Select Client')
	// 						->options(function ($record) {
	// 							return \App\Models\Company::pluck('name', 'id');
	// 						})
	// 						->searchable()
	// 						->required(),

	// 					RichEditor::make('client_address')
	// 						->extraInputAttributes(['style' => 'min-height: 5.5rem; max-height: 5.5vh; overflow-y: auto;'])
	// 						->toolbarButtons([])
	// 						->columnSpanFull(),
	// 					]),
	// 			Section::make('Invoice Details')
	// 				->collapsed()
	// 				->schema([
	// 					TableRepeater::make('invDetails')
	// 						->columns(12)
	// 						->hiddenLabel()
	// 						->relationship('invoiceDetails')
	// 						->emptyLabel('There are no item placed in the list.')
	// 						->addActionLabel('Add Item')
	// 						->columnSpanFull()
	// 						->headers([
	// 							Header::make('Description')->width('40%')->markAsRequired(),
	// 							Header::make('Qty')->width('15%')->markAsRequired(),
	// 							Header::make('Price')->width('20%')->markAsRequired(),
	// 							Header::make('Total Price')->width('20%'),
	// 						])
	// 						->afterStateUpdated(function (callable $get, callable $set) {
	// 							// static::calculateFields($get, $set);
	// 						})
	// 						->schema([
	// 							RichEditor::make('description')
	// 								->extraInputAttributes(['style' => 'min-height: 10rem; max-height: 10vh; overflow-y: auto;'])
	// 								->toolbarButtons([
	// 								]),
	// 							TextInput::make('quantity')
	// 								->numeric()
	// 								->columnSpan(2)
	// 								->required()
	// 								->extraInputAttributes(['class'=>'text-end'])
	// 								->afterStateUpdated(function (callable $set, $state, callable $get) {
	// 									$set('sub_total',$state * $get('price'));
	// 									// static::calculateFields($get, $set);
	// 								}),
	// 							TextInput::make('price')
	// 								->numeric()
	// 								->columnSpan(2)
	// 								->required()
	// 								->prefix(fn ($get) => $get('../../currency_symbol'))
	// 								->extraInputAttributes(['class'=>'text-end'])
	// 								->afterStateUpdated(function (callable $set, $state, callable $get) {
	// 									$set('sub_total',$state * $get('quantity'));
	// 									// static::calculateFields($get, $set);
	// 								}),
	// 							TextInput::make('sub_total')
	// 								->numeric()
	// 								->columnSpan(2)
	// 								->required()
	// 								->prefix(fn ($get) => $get('../../currency_symbol'))
	// 								->extraInputAttributes(['class'=>'text-end']),
	// 						])
	// 						->columnSpanFull(),

    //                     Placeholder::make('line')
	// 						->hiddenLabel()->columnSpanFull()
	// 						->content(new HtmlString('<hr>')),

	// 				]),
	// 			Section::make('Bank Information')
	// 				->collapsed(),

    //         ]);
    // }

	// protected static function updateInvoiceNumberPlaceholder(callable $set, $state): void
    // {
    //     if (!$state) {
    //         $set('invoice_no_placeholder', 'Invoice No');
    //         return;
    //     }

    //     $lastInvoice = Invoice::where('company_id', $state)
    //         ->orderBy('created_at', 'desc')
    //         ->first();

    //     $placeholder = $lastInvoice ? 'Last: ' . $lastInvoice->invoice_no : 'Invoice No';
    //     $set('invoice_no_placeholder', $placeholder);
    // }

    // protected function mutateFormDataBeforeCreate(array $data): array
    // {
    //     return InvoiceResource::processBankCustomColumnsData($data);
    // }
}
