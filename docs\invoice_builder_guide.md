# Invoice Builder - Panduan Penggunaan

## 📋 Overview

Invoice Builder adalah sistem visual untuk membuat dan mengedit layout invoice yang fleksibel. Sistem ini memungkinkan Anda untuk:

- Membuat layout invoice custom untuk setiap company
- Mengatur posisi field dengan sistem grid
- Menambah/mengurangi baris dan kolom
- Mengatur styling untuk setiap cell
- Menyimpan layout untuk digunakan dalam PDF generation

## 🏗️ Struktur Database

### Tables
1. **invoice_layouts** - Layout utama untuk company
2. **invoice_layout_sections** - Section dalam layout (header, body, footer)
3. **invoice_layout_cells** - Cell individual dalam grid (opsional, untuk future use)

### Relationships
- Company `hasOne` InvoiceLayout
- InvoiceLayout `hasMany` InvoiceLayoutSection
- InvoiceLayoutSection `hasMany` InvoiceLayoutCell

## 🚀 Cara Menggunakan

### 1. Akses Invoice Builder
- Masuk ke Company Resource di Filament Admin
- Pilih company yang ingin diedit
- Klik tab "Invoice Layout" atau akses melalui URL: `/admin/companies/{id}/invoice-layout`

### 2. Edit Layout Sections
Setiap layout memiliki 3 section utama:

#### Header Section
- Logo company
- Nama company
- Alamat company
- Informasi kontak

#### Body Section
- Informasi client
- Detail invoice
- Tanggal dan nomor invoice
- Tabel item
- Total amount

#### Footer Section
- Terms & conditions
- Signature
- Footer text

### 3. Grid System
- Setiap section menggunakan sistem grid 2D (rows x columns)
- Setiap cell dapat dikonfigurasi:
  - **Type**: text atau image
  - **Field**: field data yang akan ditampilkan
  - **Style**: CSS class untuk styling
  - **Colspan/Rowspan**: untuk merge cells

### 4. Available Fields

#### Header Fields
- `company.name` - Nama Company
- `company.address` - Alamat Company
- `company.phone` - Telepon
- `company.email` - Email
- `company.logo` - Logo Company
- `invoice.title` - Judul Invoice
- `invoice.date` - Tanggal Invoice
- `invoice.number` - Nomor Invoice

#### Body Fields
- `client.name` - Nama Client
- `client.address` - Alamat Client
- `invoice.amount` - Jumlah Invoice
- `invoice.currency` - Mata Uang
- `invoice.details` - Tabel Detail Invoice
- `bank.name` - Nama Bank
- `bank.account_number` - Nomor Rekening

#### Footer Fields
- `invoice.terms` - Terms & Conditions
- `invoice.notes` - Catatan
- `company.signature` - Tanda Tangan

## 🛠️ Fitur Builder

### Menambah Row/Column
- Klik tombol "+ Row" untuk menambah baris baru
- Klik tombol "+ Column" untuk menambah kolom baru

### Edit Cell
- Hover pada cell untuk melihat tombol edit (⚙️)
- Klik tombol edit untuk membuka panel konfigurasi
- Atur type, field, dan style sesuai kebutuhan

### Reset Layout
- Klik tombol "♻️ Reset Layout" untuk mengembalikan ke grid 2x2 default

### Hapus Cell
- Dalam panel edit cell, klik tombol "🗑 Hapus"
- Cell akan dihapus dan grid akan direindex otomatis

### Simpan Layout
- Klik tombol "💾 Simpan Layout" untuk menyimpan perubahan
- Layout akan tersimpan ke database

## 🔧 Setup dan Installation

### 1. Jalankan Migration
```bash
php artisan migrate
```

### 2. Jalankan Seeder (Opsional)
```bash
php artisan db:seed --class=InvoiceLayoutSeeder
```

### 3. Pastikan Relasi Model
Pastikan model Company memiliki relasi:
```php
public function invoiceLayout()
{
    return $this->hasOne(InvoiceLayout::class, 'company_id', 'id');
}
```

## 🐛 Troubleshooting

### Layout Tidak Muncul
1. Pastikan migration sudah dijalankan
2. Cek apakah company memiliki invoice layout
3. Refresh halaman untuk auto-create layout

### Error Saat Menyimpan
1. Cek log Laravel untuk detail error
2. Pastikan struktur grid valid
3. Pastikan field yang dipilih tersedia

### Grid Kosong
1. Klik "♻️ Reset Layout" untuk mengembalikan grid default
2. Atau tambah row/column manual

## 📝 Catatan Pengembangan

### Extending Fields
Untuk menambah field baru, edit method `getAvailableFields()` di `SectionEditor.php`:

```php
protected function getAvailableFields(string $type): array
{
    return match ($type) {
        'header' => [
            'new.field' => 'New Field Label',
            // ... existing fields
        ],
        // ... other sections
    };
}
```

### Custom Styling
Setiap cell dapat memiliki custom CSS class melalui field `style.class`.

### Future Enhancements
- Drag & drop untuk reorder cells
- Preview mode untuk melihat hasil layout
- Template library untuk layout siap pakai
- Export/import layout antar company
