# Debug 404 Invoice Layout

## Masalah yang <PERSON>temukan

Be<PERSON>kan log error dan analisis kode, masalah 404 pada invoiceLayout disebabkan oleh:

### 1. **Class Loading Issue**
```
Class "App\Filament\Admin\Resources\CompanyResource\Pages\EditCompanyInvoiceLayout" not found
```

### 2. **Kemungkinan Penyebab**

#### A. **Cache Issue**
- Filament cache mungkin masih menyimpan versi lama
- Laravel cache perlu dibersihkan
- Autoloader perlu di-refresh

#### B. **Namespace/Import Issue**
- File ada tapi tidak ter-autoload dengan benar
- Kemungkinan ada typo di namespace

#### C. **File Permission Issue**
- File mungkin tidak readable oleh web server

### 3. **Langkah Troubleshooting**

#### Step 1: Clear All Caches
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan filament:clear-cached-components
composer dump-autoload
```

#### Step 2: Verify File Structure
```
app/
├── Filament/
│   └── Admin/
│       └── Resources/
│           └── CompanyResource/
│               └── Pages/
│                   ├── EditCompanyInvoiceLayout.php ✓
│                   ├── EditCompany.php ✓
│                   └── ListCompanies.php ✓
```

#### Step 3: Check Namespace
File: `app/Filament/Admin/Resources/CompanyResource/Pages/EditCompanyInvoiceLayout.php`
```php
<?php
namespace App\Filament\Admin\Resources\CompanyResource\Pages;
// ✓ Namespace correct
```

#### Step 4: Check Class Declaration
```php
class EditCompanyInvoiceLayout extends Page
// ✓ Class name correct
```

#### Step 5: Check Import in CompanyResource
```php
use App\Filament\Admin\Resources\CompanyResource\Pages;
// ✓ Import correct

'invoiceLayout' => Pages\EditCompanyInvoiceLayout::route('/{record}/invoice-layout'),
// ✓ Reference correct
```

### 4. **Solusi yang Sudah Diterapkan**

1. ✅ **Fixed Property Mismatch**: Changed `$record` to `$company`
2. ✅ **Fixed View Reference**: Updated view to use correct property
3. ✅ **Fixed Missing Methods**: Added all required methods
4. ✅ **Fixed Namespace**: Ensured correct namespace structure

### 5. **Langkah Selanjutnya**

#### Option 1: Manual Cache Clear
Jalankan file `clear_cache.bat` yang sudah dibuat

#### Option 2: Restart Web Server
Restart Apache/Nginx untuk memastikan tidak ada cache di level server

#### Option 3: Check File Permissions
Pastikan file dapat dibaca oleh web server:
```bash
chmod 644 app/Filament/Admin/Resources/CompanyResource/Pages/EditCompanyInvoiceLayout.php
```

#### Option 4: Verify Autoloader
```bash
composer dump-autoload -o
```

### 6. **Test URL**
Setelah clear cache, test URL:
```
/admin/companies/{id}/invoice-layout
```

### 7. **Alternative Debugging**
Jika masih 404, coba akses langsung melalui:
1. Company list → klik "Invoice Layout" button
2. Company edit → klik "Edit Layout Invoice" di header actions

### 8. **Expected Behavior**
Setelah fix:
- URL `/admin/companies/{id}/invoice-layout` harus accessible
- Page harus menampilkan 3 sections: Header, Body, Footer
- Setiap section harus memiliki grid editor
- Auto-create layout jika belum ada
