<?php

// Test script untuk memeriksa routing
require_once 'vendor/autoload.php';

use App\Filament\Admin\Resources\CompanyResource;

echo "Testing CompanyResource routing...\n\n";

try {
    // Test getPages method
    $pages = CompanyResource::getPages();
    
    echo "Available pages:\n";
    foreach ($pages as $name => $page) {
        echo "- {$name}: " . get_class($page) . "\n";
    }
    
    echo "\nTesting invoiceLayout page:\n";
    if (isset($pages['invoiceLayout'])) {
        echo "✓ invoiceLayout page found\n";
        echo "Class: " . get_class($pages['invoiceLayout']) . "\n";
        
        // Test if class exists
        $className = 'App\\Filament\\Admin\\Resources\\CompanyResource\\Pages\\EditCompanyInvoiceLayout';
        if (class_exists($className)) {
            echo "✓ EditCompanyInvoiceLayout class exists\n";
        } else {
            echo "✗ EditCompanyInvoiceLayout class NOT found\n";
        }
    } else {
        echo "✗ invoiceLayout page NOT found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\nDone.\n";
