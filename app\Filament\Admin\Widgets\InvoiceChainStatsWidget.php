<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class InvoiceChainStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Get chain statistics
        $totalInvoices = Invoice::count();
        $mainInvoices = Invoice::whereNull('parent_invoice_id')->count();
        $childInvoices = Invoice::whereNotNull('parent_invoice_id')->count();
        
        // Get average chain length
        $avgChainLength = $this->getAverageChainLength();
        
        // Get longest chain
        $longestChain = $this->getLongestChainLength();
        
        // Get completed chains (closed invoices)
        $completedChains = Invoice::where('status', 'Closed')->count();
        
        // Get active chains (draft + issued)
        $activeChains = Invoice::whereIn('status', ['Draft', 'Issued'])->count();
        
        return [
            Stat::make('Total Invoices', $totalInvoices)
                ->description('All invoices in system')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),
                
            Stat::make('Main Invoices', $mainInvoices)
                ->description('Starting points of chains')
                ->descriptionIcon('heroicon-m-play')
                ->color('success'),
                
            Stat::make('Child Invoices', $childInvoices)
                ->description('Chain continuations')
                ->descriptionIcon('heroicon-m-link')
                ->color('info'),
                
            Stat::make('Average Chain Length', number_format($avgChainLength, 1))
                ->description('Companies per transaction')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('warning'),
                
            Stat::make('Longest Chain', $longestChain)
                ->description('Maximum chain depth')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('danger'),
                
            Stat::make('Completed Chains', $completedChains)
                ->description('Closed transactions')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
        ];
    }
    
    private function getAverageChainLength(): float
    {
        // Get all main invoices and calculate their chain lengths
        $mainInvoices = Invoice::whereNull('parent_invoice_id')->get();
        $totalLength = 0;
        $chainCount = 0;
        
        foreach ($mainInvoices as $mainInvoice) {
            $chainLength = $this->calculateChainLength($mainInvoice);
            $totalLength += $chainLength;
            $chainCount++;
        }
        
        return $chainCount > 0 ? $totalLength / $chainCount : 0;
    }
    
    private function getLongestChainLength(): int
    {
        $mainInvoices = Invoice::whereNull('parent_invoice_id')->get();
        $maxLength = 0;
        
        foreach ($mainInvoices as $mainInvoice) {
            $chainLength = $this->calculateChainLength($mainInvoice);
            $maxLength = max($maxLength, $chainLength);
        }
        
        return $maxLength;
    }
    
    private function calculateChainLength(Invoice $invoice): int
    {
        $length = 1; // Count the current invoice
        $current = $invoice;
        
        // Follow the chain to the end
        while ($current->childInvoice()->exists()) {
            $current = $current->childInvoice()->first();
            $length++;
        }
        
        return $length;
    }
    
    protected function getColumns(): int
    {
        return 3; // Display 3 stats per row
    }
}
