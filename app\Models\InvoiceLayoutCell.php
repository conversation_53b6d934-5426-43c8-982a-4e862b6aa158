<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceLayoutCell extends Model
{
    protected $table = 'invoice_layout_cells';

    protected $fillable = [
        'layout_section_id',
        'row',
        'col',
        'rowspan',
        'colspan',
        'type',
        'content',
        'style',
    ];

	protected $casts = [
        'style' => 'array',
    ];

    public function section(): BelongsTo
    {
        return $this->belongsTo(InvoiceLayoutSection::class, 'layout_section_id');
    }
}
