<?php

namespace App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Filament\Admin\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class EditInvoice extends EditRecord
{
    protected static string $resource = InvoiceResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function getSubheading(): string|Htmlable
    {
        return new HtmlString(view('components.invoice-quick-navigation', [
            'invoice' => $this->record,
            'mode' => 'edit'
        ])->render());
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return InvoiceResource::mutateBankCustomColumnsDataBeforeFill($data);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        return InvoiceResource::processBankCustomColumnsData($data);
    }
}
